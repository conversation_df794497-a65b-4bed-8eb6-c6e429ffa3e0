# Enhanced Blockchain Wallet Generator Configuration
# Educational and Security Research Tool

# Application Settings
application:
  name: "Enhanced Blockchain Wallet Generator"
  version: "2.0.0"
  description: "Educational blockchain security research tool"
  author: "Security Research Team"
  license: "Educational Use Only"

# Cryptographic Security Settings
cryptography:
  # Secure random number generation
  entropy_sources:
    - "os.urandom"
    - "secrets"
    - "cryptography.hazmat"
  minimum_entropy_bits: 256
  key_validation:
    enabled: true
    check_weak_patterns: true
    validate_checksums: true
  
  # Key generation parameters
  key_generation:
    use_hardware_rng: false  # Set to true if hardware RNG available
    entropy_pool_size: 4096
    reseed_interval: 1000
    validate_randomness: true

# Cryptocurrency Support
cryptocurrencies:
  bitcoin:
    enabled: true
    network: "mainnet"  # mainnet, testnet
    address_types: ["p2pkh", "p2sh", "bech32"]
    api_providers:
      primary: "blockcypher"
      fallback: ["blockstream", "blockchain_info"]
  
  ethereum:
    enabled: true
    network: "mainnet"
    api_providers:
      primary: "etherscan"
      fallback: ["infura", "alchemy"]
  
  litecoin:
    enabled: true
    network: "mainnet"
    api_providers:
      primary: "blockcypher"
      fallback: ["blockstream"]
  
  bitcoin_cash:
    enabled: true
    network: "mainnet"
    api_providers:
      primary: "blockcypher"
      fallback: ["blockstream"]

# API Configuration
api:
  rate_limiting:
    requests_per_second: 1.0
    burst_limit: 5
    exponential_backoff:
      enabled: true
      base_delay: 1.0
      max_delay: 60.0
      multiplier: 2.0
      max_retries: 3
  
  timeout:
    connect: 10
    read: 30
    total: 45
  
  caching:
    enabled: true
    ttl_seconds: 300
    max_entries: 10000

# Performance Settings
performance:
  batch_processing:
    enabled: true
    batch_size: 100
    max_concurrent_batches: 5
  
  threading:
    max_workers: 4
    thread_safety: true
    worker_timeout: 300
  
  memory_management:
    clear_sensitive_data: true
    gc_interval: 1000
    max_memory_mb: 1024

# Educational Security Research
security_research:
  weak_entropy_detection:
    enabled: true
    patterns:
      - "sequential"
      - "repeated"
      - "low_entropy"
      - "known_weak"
  
  vulnerability_analysis:
    check_known_ranges: true
    analyze_derivation_paths: true
    detect_poor_practices: true
  
  educational_examples:
    include_weak_keys: false  # Only for controlled educational environments
    demonstrate_vulnerabilities: true

# Data Management
data:
  output:
    format: "json"
    schema_validation: true
    include_metadata: true
    compress_large_files: true
  
  storage:
    base_directory: "./output"
    backup_enabled: true
    backup_interval_hours: 24
    retention_days: 30
  
  security:
    encrypt_sensitive_data: false  # Set to true for production
    file_permissions: "600"
    secure_deletion: true

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "structured"  # structured, simple
  output:
    console: true
    file: true
    file_path: "./logs/wallet_generator.log"
  
  audit:
    enabled: true
    log_all_operations: true
    include_timestamps: true
    log_api_calls: true

# Network and Privacy
network:
  proxy:
    enabled: false
    type: "socks5"  # http, https, socks4, socks5
    host: "127.0.0.1"
    port: 9050
  
  user_agent: "Educational-Research-Tool/2.0"
  verify_ssl: true
  connection_pool_size: 10

# Ethical and Legal Compliance
compliance:
  educational_only: true
  require_acknowledgment: true
  log_user_acceptance: true
  
  warnings:
    display_probability_warning: true
    display_legal_disclaimer: true
    display_ethical_guidelines: true
  
  safeguards:
    max_daily_generations: 10000
    require_research_purpose: true
    log_all_activities: true

# Testing and Development
testing:
  mock_api_responses: false
  use_testnet: false
  enable_debug_mode: false
  
  unit_tests:
    enabled: true
    coverage_threshold: 90
  
  integration_tests:
    enabled: true
    test_api_connectivity: true

# Monitoring and Alerts
monitoring:
  performance_tracking: true
  error_reporting: true
  usage_statistics: true
  
  alerts:
    high_error_rate: true
    unusual_patterns: true
    security_violations: true
