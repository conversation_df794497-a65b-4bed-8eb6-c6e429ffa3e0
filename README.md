# مولد محافظ البلوك تشين العشوائية
# Random Blockchain Wallet Generator

## الوصف
هذا المشروع يحتوي على سكريبتين Python لإنشاء مفاتيح البلوك تشين العشوائية والتحقق من أرصدة المحافظ:

1. **blockchain_wallet_generator.py** - النسخة الأساسية
2. **advanced_wallet_generator.py** - النسخة المتقدمة مع ميزات إضافية

## الميزات

### النسخة الأساسية:
- ✅ إنشاء مفاتيح خاصة عشوائية آمنة
- ✅ تحويل المفاتيح إلى عناوين Bitcoin
- ✅ فحص أرصدة Bitcoin باستخدام BlockCypher API
- ✅ نظام تسجيل مفصل (Logger)
- ✅ حفظ المحافظ التي تحتوي على أرصدة
- ✅ إحصائيات مفصلة

### النسخة المتقدمة:
- ✅ جميع ميزات النسخة الأساسية
- ✅ دعم تنسيق WIF للمفاتيح الخاصة
- ✅ نظام تسجيل ملون
- ✅ معالجة متعددة الخيوط (Multi-threading)
- ✅ حد معدل الطلبات (Rate limiting)
- ✅ دعم Ethereum (قيد التطوير)
- ✅ تقارير تقدم محسنة

## التثبيت

### 1. تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث:
```bash
python --version
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install requests base58
```

## الاستخدام

### تشغيل النسخة الأساسية:
```bash
python blockchain_wallet_generator.py
```

### تشغيل النسخة المتقدمة:
```bash
python advanced_wallet_generator.py
```

### المعاملات المطلوبة:
- **عدد المحافظ**: العدد الأقصى للمحافظ المراد إنشاؤها
- **التأخير**: الوقت بالثواني بين كل محاولة (لتجنب حظر API)
- **عدد الخيوط**: (النسخة المتقدمة فقط) عدد الخيوط للمعالجة المتوازية

## ملفات الإخراج

### ملفات السجل:
- `wallet_generator.log` - سجل النسخة الأساسية
- `advanced_wallet_generator.log` - سجل النسخة المتقدمة

### ملفات البيانات:
- `wallets_with_balance.json` - المحافظ التي تحتوي على أرصدة (النسخة الأساسية)
- `found_wallets_bitcoin.json` - محافظ Bitcoin بأرصدة (النسخة المتقدمة)

## مثال على الإخراج

```
🚀 تم تشغيل مولد محافظ البلوك تشين
============================================================
🎯 بدء البحث عن المحافظ - العدد الأقصى: 100
⏱️ التأخير بين كل محاولة: 1.0 ثانية
============================================================

🔑 المحفظة #1
   المفتاح الخاص: a1b2c3d4e5f6...
   العنوان: **********************************
💸 رصيد فارغ: 0.0 BTC

🔑 المحفظة #2
   المفتاح الخاص: f6e5d4c3b2a1...
   العنوان: **********************************
💰 تم العثور على رصيد! 0.00001234 BTC
🎉 المفتاح الخاص: f6e5d4c3b2a1...
🎉 العنوان: **********************************
============================================================
```

## تحذيرات مهمة

⚠️ **تحذيرات الأمان:**
- هذا السكريبت للأغراض التعليمية والبحثية فقط
- احتمالية العثور على محفظة بأرصدة منخفضة جداً (1 في 2^160)
- لا تستخدم هذا السكريبت لأغراض غير قانونية
- احترم حدود معدل API لتجنب الحظر

⚠️ **تحذيرات تقنية:**
- استخدم تأخير مناسب بين الطلبات (1-2 ثانية على الأقل)
- BlockCypher API له حدود يومية مجانية
- قد تحتاج إلى API key للاستخدام المكثف

## التخصيص

### تغيير مزود API:
يمكنك تعديل دالة `check_bitcoin_balance` لاستخدام مزودي API آخرين:
- Blockchain.info
- Blockstream
- BitPay Insight

### إضافة عملات أخرى:
يمكن توسيع السكريبت لدعم عملات أخرى مثل:
- Ethereum (ETH)
- Litecoin (LTC)
- Bitcoin Cash (BCH)

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تحقق من اتصال الإنترنت
3. راجع ملفات السجل للأخطاء
4. تأكد من عدم تجاوز حدود API

## الترخيص

هذا المشروع مفتوح المصدر للأغراض التعليمية. استخدمه بمسؤولية.

---

**ملاحظة**: هذا السكريبت يستخدم خوارزميات مبسطة لإنشاء العناوين. للاستخدام الإنتاجي، يُنصح باستخدام مكتبات متخصصة مثل `bitcoinlib` أو `web3.py`.
