#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشغيل سريع لمولد محافظ البلوك تشين
Quick launcher for blockchain wallet generator
"""

import sys
import os

def main():
    print("🌟 مولد محافظ البلوك تشين")
    print("=" * 40)
    print("1. النسخة الأساسية")
    print("2. النسخة المتقدمة")
    print("3. خروج")
    print("=" * 40)
    
    while True:
        choice = input("اختر النسخة (1-3): ").strip()
        
        if choice == "1":
            print("🚀 تشغيل النسخة الأساسية...")
            os.system("python blockchain_wallet_generator.py")
            break
        elif choice == "2":
            print("🚀 تشغيل النسخة المتقدمة...")
            os.system("python advanced_wallet_generator.py")
            break
        elif choice == "3":
            print("👋 وداعاً!")
            sys.exit(0)
        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")

if __name__ == "__main__":
    main()
