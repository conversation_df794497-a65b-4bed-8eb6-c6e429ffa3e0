#!/usr/bin/env python3
"""
Debug test script for the enhanced wallet generator.
"""

import sys
import os
import traceback

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_crypto_security():
    """Test crypto security module."""
    print("Testing crypto security module...")

    try:
        from crypto_security import SecureRandomGenerator, KeyValidator, KeyValidationResult

        # Create basic config
        config = {
            'entropy_sources': ['system', 'urandom'],
            'entropy_pool_size': 1000,
            'min_entropy_threshold': 7.0,
            'check_weak_keys': True,
            'check_patterns': True,
            'validation_enabled': True
        }

        # Test random generator
        rng = SecureRandomGenerator(config)
        print("✓ SecureRandomGenerator created")
        
        # Test private key generation
        private_key_bytes, metrics = rng.generate_private_key()
        print(f"✓ Private key generated: {len(private_key_bytes)} bytes")
        print(f"  Type: {type(private_key_bytes)}")
        print(f"  Metrics: {metrics}")
        
        # Test key validator
        validator = KeyValidator(config)
        result = validator.validate_private_key(private_key_bytes)
        print(f"✓ Key validation result: {result}")
        
        return private_key_bytes
        
    except Exception as e:
        print(f"❌ Error in crypto security: {e}")
        traceback.print_exc()
        return None

def test_cryptocurrency(private_key_bytes):
    """Test cryptocurrency module."""
    print("\nTesting cryptocurrency module...")
    
    try:
        from cryptocurrency import CryptocurrencyFactory, CryptocurrencyType
        
        # Test Bitcoin
        bitcoin = CryptocurrencyFactory.create_cryptocurrency(CryptocurrencyType.BITCOIN)
        print("✓ Bitcoin instance created")
        
        # Test wallet creation
        print(f"Private key type: {type(private_key_bytes)}")
        print(f"Private key length: {len(private_key_bytes)}")
        
        wallet = bitcoin.create_wallet(private_key_bytes)
        print("✓ Bitcoin wallet created")
        print(f"  Addresses: {len(wallet.addresses)}")
        
        return wallet
        
    except Exception as e:
        print(f"❌ Error in cryptocurrency: {e}")
        traceback.print_exc()
        return None

def main():
    """Main test function."""
    print("Enhanced Wallet Generator Debug Test")
    print("=" * 50)
    
    # Test crypto security
    private_key_bytes = test_crypto_security()
    if private_key_bytes is None:
        return
    
    # Test cryptocurrency
    wallet = test_cryptocurrency(private_key_bytes)
    if wallet is None:
        return
    
    print("\n✓ All tests passed!")
    print(f"Sample wallet address: {wallet.addresses[0].address}")

if __name__ == "__main__":
    main()
