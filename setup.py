#!/usr/bin/env python3
"""
Setup Script for Enhanced Blockchain Wallet Generator

This script sets up the enhanced wallet generator environment with all
necessary dependencies, configuration, and directory structure.

Author: Security Research Team
License: Educational Use Only
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """Print setup banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                Enhanced Blockchain Wallet Generator Setup                    ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║ This setup script will install all dependencies and configure the           ║
║ enhanced wallet generator for educational and security research purposes.    ║
║                                                                              ║
║ EDUCATIONAL USE ONLY - NOT FOR ILLEGAL ACTIVITIES                           ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_system_requirements():
    """Check system requirements."""
    print("\n🔍 Checking system requirements...")
    
    system = platform.system()
    print(f"✅ Operating System: {system}")
    
    # Check available memory
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"✅ Available Memory: {memory_gb:.1f} GB")
        
        if memory_gb < 2:
            print("⚠️  Warning: Less than 2GB RAM available. Performance may be limited.")
    except ImportError:
        print("ℹ️  Memory check skipped (psutil not available)")
    
    return True


def create_directory_structure():
    """Create necessary directory structure."""
    print("\n📁 Creating directory structure...")
    
    directories = [
        "logs",
        "output",
        "tests",
        "src",
        "docs",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        # Check if requirements.txt exists
        if not Path("requirements.txt").exists():
            print("❌ requirements.txt not found!")
            return False
        
        # Install dependencies
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def setup_configuration():
    """Set up configuration files."""
    print("\n⚙️  Setting up configuration...")
    
    # Check if config.yaml exists
    if Path("config.yaml").exists():
        print("✅ Configuration file already exists")
        return True
    
    # Create basic configuration if it doesn't exist
    basic_config = """# Enhanced Wallet Generator Configuration
# Educational and Security Research Tool

# Cryptographic Security Settings
cryptography:
  entropy_sources:
    - "system"
    - "urandom"
  entropy_pool_size: 1000
  min_entropy_threshold: 7.0
  check_weak_keys: true
  check_patterns: true
  validation_enabled: true

# Cryptocurrency Support
cryptocurrencies:
  bitcoin:
    enabled: true
    networks: ["mainnet", "testnet"]
    address_types: ["p2pkh", "p2sh", "bech32"]
  ethereum:
    enabled: true
    networks: ["mainnet", "testnet"]
    address_types: ["standard"]
  litecoin:
    enabled: true
    networks: ["mainnet", "testnet"]
    address_types: ["p2pkh", "p2sh", "bech32"]

# Performance Settings
performance:
  max_concurrent_batches: 4
  batch_size: 100
  batch_timeout_seconds: 30
  memory_limit_mb: 1024
  enable_progress_tracking: true
  enable_performance_monitoring: true

# Data Management
data:
  base_directory: "./output"
  file_permissions: "600"
  compress_large_files: true
  compression_threshold_mb: 10
  backup_enabled: true
  schema_validation: true

# Security Research Settings
security_research:
  min_entropy_threshold: 7.0
  pattern_detection: true
  vulnerability_analysis: true
  weak_key_detection: true
  educational_mode: true

# Ethical Compliance
compliance:
  require_acknowledgment: true
  max_daily_generations: 10000
  audit_logging: true
  educational_warnings: true
  legal_disclaimer: true

# API Configuration (Optional - for balance checking)
api:
  enabled: false
  # Uncomment and configure if you have API keys
  # blockcypher:
  #   api_key: "your_api_key_here"
  #   rate_limit: 5
  # etherscan:
  #   api_key: "your_api_key_here"
  #   rate_limit: 5

# Monitoring and Logging
monitoring:
  log_level: "INFO"
  log_file: "logs/enhanced_wallet_generator.log"
  performance_monitoring: true
  memory_monitoring: true
  enable_metrics: true
"""
    
    try:
        with open("config.yaml", "w") as f:
            f.write(basic_config)
        print("✅ Configuration file created")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration: {e}")
        return False


def run_tests():
    """Run test suite to verify installation."""
    print("\n🧪 Running test suite...")
    
    try:
        # Check if test file exists
        test_file = Path("tests/test_enhanced_wallet_generator.py")
        if not test_file.exists():
            print("⚠️  Test file not found, skipping tests")
            return True
        
        # Run tests
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("⚠️  Some tests failed, but installation can continue")
            print("   Run 'python -m pytest tests/ -v' for detailed test results")
            return True
            
    except FileNotFoundError:
        print("ℹ️  pytest not available, running basic tests...")
        
        # Try to run basic test
        try:
            result = subprocess.run([
                sys.executable, "tests/test_enhanced_wallet_generator.py"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Basic tests passed")
            else:
                print("⚠️  Basic tests had issues, but installation can continue")
            
            return True
            
        except Exception as e:
            print(f"ℹ️  Could not run tests: {e}")
            return True


def display_usage_instructions():
    """Display usage instructions."""
    instructions = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                            SETUP COMPLETE                                   ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║ The Enhanced Blockchain Wallet Generator is now ready for use!              ║
║                                                                              ║
║ USAGE EXAMPLES:                                                              ║
║                                                                              ║
║ Basic usage:                                                                 ║
║   python enhanced_wallet_generator.py --crypto bitcoin --count 100          ║
║                                                                              ║
║ Generate Ethereum wallets:                                                   ║
║   python enhanced_wallet_generator.py --crypto ethereum --count 50          ║
║                                                                              ║
║ Skip balance checking (faster):                                              ║
║   python enhanced_wallet_generator.py --crypto bitcoin --count 1000 \\       ║
║                                        --no-balance-check                   ║
║                                                                              ║
║ Custom batch size:                                                           ║
║   python enhanced_wallet_generator.py --crypto litecoin --count 500 \\       ║
║                                        --batch-size 50                      ║
║                                                                              ║
║ IMPORTANT REMINDERS:                                                         ║
║ • This tool is for EDUCATIONAL purposes only                                ║
║ • The probability of finding funded wallets is astronomically low           ║
║ • Always comply with applicable laws and regulations                        ║
║ • Use responsibly for learning about blockchain security                    ║
║                                                                              ║
║ For help: python enhanced_wallet_generator.py --help                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(instructions)


def main():
    """Main setup function."""
    print_banner()
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    if not check_system_requirements():
        sys.exit(1)
    
    # Setup steps
    steps = [
        ("Creating directory structure", create_directory_structure),
        ("Installing dependencies", install_dependencies),
        ("Setting up configuration", setup_configuration),
        ("Running tests", run_tests)
    ]
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        if not step_function():
            print(f"❌ Setup failed at: {step_name}")
            sys.exit(1)
    
    # Display completion message
    print("\n" + "="*80)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("="*80)
    
    display_usage_instructions()


if __name__ == "__main__":
    main()
