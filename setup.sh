#!/bin/bash

echo "🚀 إعداد مولد محافظ البلوك تشين"
echo "=================================="

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت. يرجى تثبيت Python3 أولاً"
    exit 1
fi

echo "✅ تم العثور على Python3"

# إنشاء البيئة الافتراضية
echo "📦 إنشاء البيئة الافتراضية..."
python3 -m venv venv

if [ $? -eq 0 ]; then
    echo "✅ تم إنشاء البيئة الافتراضية بنجاح"
else
    echo "❌ فشل في إنشاء البيئة الافتراضية"
    exit 1
fi

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت المكتبات
echo "📚 تثبيت المكتبات المطلوبة..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ تم تثبيت جميع المكتبات بنجاح"
else
    echo "❌ فشل في تثبيت المكتبات"
    exit 1
fi

# إنشاء سكريبت التشغيل
echo "📝 إنشاء سكريبت التشغيل..."
cat > start.sh << 'EOF'
#!/bin/bash
echo "🌟 تشغيل مولد محافظ البلوك تشين"
echo "================================"
source venv/bin/activate
python run.py
EOF

chmod +x start.sh

echo ""
echo "🎉 تم الإعداد بنجاح!"
echo "================================"
echo "لتشغيل البرنامج:"
echo "  ./start.sh"
echo ""
echo "أو يدوياً:"
echo "  source venv/bin/activate"
echo "  python blockchain_wallet_generator.py"
echo "  python advanced_wallet_generator.py"
echo ""
echo "⚠️  تذكر: استخدم هذا السكريبت بمسؤولية وللأغراض التعليمية فقط"
