#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Wallet Generator

This module provides extensive unit tests, integration tests, and security
validation tests for all components of the enhanced wallet generator system.

Author: Security Research Team
License: Educational Use Only
"""

import unittest
import asyncio
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import yaml

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from crypto_security import SecureRandomGenerator, KeyValidator, KeyValidationResult
from cryptocurrency import CryptocurrencyFactory, CryptocurrencyType
from api_integration import APIManager, BalanceInfo
from performance import BatchProcessor, ProgressTracker, MemoryManager
from data_management import DataExporter, WalletRecord, WalletMetadata, SchemaValidator
from security_research import VulnerabilityAnalyzer, VulnerabilityType, SecurityLevel


class TestSecureRandomGenerator(unittest.TestCase):
    """Test cases for SecureRandomGenerator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'entropy_sources': ['system', 'urandom'],
            'entropy_pool_size': 1000,
            'min_entropy_threshold': 7.0
        }
        self.generator = SecureRandomGenerator(self.config)
    
    def test_generate_private_key(self):
        """Test private key generation."""
        private_key, metrics = self.generator.generate_private_key()
        
        # Check key length
        self.assertEqual(len(private_key), 32)
        
        # Check metrics
        self.assertIn('entropy_estimate', metrics.__dict__)
        self.assertIn('generation_time', metrics.__dict__)
        self.assertIn('source_used', metrics.__dict__)
        
        # Check entropy quality
        self.assertGreaterEqual(metrics.entropy_estimate, 6.0)
    
    def test_generate_multiple_keys_unique(self):
        """Test that multiple generated keys are unique."""
        keys = []
        for _ in range(100):
            key, _ = self.generator.generate_private_key()
            keys.append(key)
        
        # All keys should be unique
        self.assertEqual(len(keys), len(set(keys)))
    
    def test_entropy_pool_management(self):
        """Test entropy pool management."""
        initial_pool_size = len(self.generator.entropy_pool)
        
        # Generate some keys to consume entropy
        for _ in range(10):
            self.generator.generate_private_key()
        
        # Pool should be refreshed
        self.assertGreaterEqual(len(self.generator.entropy_pool), initial_pool_size * 0.8)


class TestKeyValidator(unittest.TestCase):
    """Test cases for KeyValidator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'check_weak_keys': True,
            'check_patterns': True,
            'min_entropy_threshold': 7.0
        }
        self.validator = KeyValidator(self.config)
    
    def test_validate_valid_key(self):
        """Test validation of valid private key."""
        # Generate a secure random key
        import secrets
        private_key = secrets.token_bytes(32)
        
        result = self.validator.validate_private_key(private_key)
        self.assertEqual(result, KeyValidationResult.VALID)
    
    def test_validate_zero_key(self):
        """Test validation of zero key (invalid)."""
        zero_key = bytes(32)  # All zeros
        
        result = self.validator.validate_private_key(zero_key)
        self.assertEqual(result, KeyValidationResult.INVALID_ZERO)
    
    def test_validate_max_key(self):
        """Test validation of maximum key (invalid for secp256k1)."""
        max_key = bytes([0xFF] * 32)
        
        result = self.validator.validate_private_key(max_key)
        self.assertEqual(result, KeyValidationResult.INVALID_RANGE)
    
    def test_validate_weak_entropy_key(self):
        """Test validation of key with weak entropy."""
        # Create a key with repeated pattern (weak entropy)
        weak_key = bytes([0x01, 0x02] * 16)
        
        result = self.validator.validate_private_key(weak_key)
        self.assertIn(result, [KeyValidationResult.WEAK_ENTROPY, KeyValidationResult.VALID])


class TestCryptocurrencyFactory(unittest.TestCase):
    """Test cases for CryptocurrencyFactory."""
    
    def test_create_bitcoin(self):
        """Test Bitcoin cryptocurrency creation."""
        bitcoin = CryptocurrencyFactory.create_cryptocurrency(CryptocurrencyType.BITCOIN)
        
        self.assertEqual(bitcoin.name, "Bitcoin")
        self.assertEqual(bitcoin.symbol, "BTC")
        self.assertIn("mainnet", bitcoin.network)
    
    def test_create_ethereum(self):
        """Test Ethereum cryptocurrency creation."""
        ethereum = CryptocurrencyFactory.create_cryptocurrency(CryptocurrencyType.ETHEREUM)
        
        self.assertEqual(ethereum.name, "Ethereum")
        self.assertEqual(ethereum.symbol, "ETH")
        self.assertIn("mainnet", ethereum.network)
    
    def test_create_wallet(self):
        """Test wallet creation."""
        bitcoin = CryptocurrencyFactory.create_cryptocurrency(CryptocurrencyType.BITCOIN)
        
        import secrets
        private_key = secrets.token_bytes(32)
        
        wallet = bitcoin.create_wallet(private_key)
        
        self.assertEqual(len(wallet.private_key_hex), 64)  # 32 bytes = 64 hex chars
        self.assertGreater(len(wallet.public_key_hex), 60)  # Compressed public key
        self.assertGreater(len(wallet.addresses), 0)  # At least one address


class TestBatchProcessor(unittest.TestCase):
    """Test cases for BatchProcessor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'max_concurrent_batches': 4,
            'batch_timeout_seconds': 30
        }
        self.processor = BatchProcessor(self.config)
    
    def test_create_batches(self):
        """Test batch creation."""
        items = list(range(100))
        batch_size = 10
        
        batches = self.processor.create_batches(items, batch_size)
        
        self.assertEqual(len(batches), 10)
        self.assertEqual(len(batches[0]), 10)
        self.assertEqual(len(batches[-1]), 10)
    
    def test_create_batches_uneven(self):
        """Test batch creation with uneven division."""
        items = list(range(95))
        batch_size = 10
        
        batches = self.processor.create_batches(items, batch_size)
        
        self.assertEqual(len(batches), 10)
        self.assertEqual(len(batches[-1]), 5)  # Last batch smaller
    
    async def test_process_batches_async(self):
        """Test asynchronous batch processing."""
        async def mock_processor(batch):
            await asyncio.sleep(0.01)  # Simulate work
            return sum(batch)
        
        items = list(range(20))
        batches = self.processor.create_batches(items, 5)
        
        results = await self.processor.process_batches_async(batches, mock_processor)
        
        self.assertEqual(len(results), 4)
        self.assertEqual(sum(results), sum(items))


class TestProgressTracker(unittest.TestCase):
    """Test cases for ProgressTracker."""
    
    def test_progress_tracking(self):
        """Test progress tracking functionality."""
        total_items = 100
        tracker = ProgressTracker(total_items)
        
        # Initial state
        self.assertEqual(tracker.current_item, 0)
        self.assertEqual(tracker.percentage, 0.0)
        
        # Update progress
        tracker.update(25)
        self.assertEqual(tracker.current_item, 25)
        self.assertEqual(tracker.percentage, 25.0)
        
        # Complete
        tracker.update(75)
        self.assertEqual(tracker.current_item, 100)
        self.assertEqual(tracker.percentage, 100.0)
    
    def test_progress_callbacks(self):
        """Test progress callback functionality."""
        callback_called = False
        callback_data = None
        
        def test_callback(progress_info):
            nonlocal callback_called, callback_data
            callback_called = True
            callback_data = progress_info
        
        tracker = ProgressTracker(100)
        tracker.add_progress_callback(test_callback)
        
        tracker.update(50)
        
        self.assertTrue(callback_called)
        self.assertEqual(callback_data.current_item, 50)
        self.assertEqual(callback_data.percentage, 50.0)


class TestSchemaValidator(unittest.TestCase):
    """Test cases for SchemaValidator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.validator = SchemaValidator()
    
    def test_validate_valid_wallet_record(self):
        """Test validation of valid wallet record."""
        valid_record = {
            "wallet_id": "test_wallet_001",
            "private_key_hex": "a" * 64,  # 64 hex characters
            "public_key_hex": "b" * 66,  # 66 hex characters (compressed)
            "addresses": {"p2pkh": "**********************************"},
            "balance_info": {"balance": 0.0},
            "transaction_history": [],
            "metadata": {
                "generation_timestamp": 1234567890.0,
                "generation_method": "test",
                "entropy_source": "test",
                "validation_status": "valid",
                "cryptocurrency": "bitcoin",
                "network": "mainnet"
            },
            "checksum": "c" * 64,  # 64 hex characters
            "schema_version": "2.0.0"
        }
        
        result = self.validator.validate_wallet_record(valid_record)
        self.assertTrue(result)
    
    def test_validate_invalid_wallet_record(self):
        """Test validation of invalid wallet record."""
        invalid_record = {
            "wallet_id": "",  # Empty wallet ID (invalid)
            "private_key_hex": "invalid_hex",  # Invalid hex format
            "addresses": {},
            "balance_info": {},
            "metadata": {},
            "checksum": "",
            "schema_version": "2.0.0"
        }
        
        with self.assertRaises(Exception):  # Should raise ValidationError
            self.validator.validate_wallet_record(invalid_record)


class TestVulnerabilityAnalyzer(unittest.TestCase):
    """Test cases for VulnerabilityAnalyzer."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'min_entropy_threshold': 7.0,
            'pattern_detection': True
        }
        self.analyzer = VulnerabilityAnalyzer(self.config)
    
    def test_analyze_secure_key(self):
        """Test analysis of secure private key."""
        import secrets
        secure_key = secrets.token_bytes(32)
        
        vulnerabilities = self.analyzer.analyze_key_security(secure_key)
        
        # Secure key should have no vulnerabilities
        self.assertEqual(len(vulnerabilities), 0)
    
    def test_analyze_weak_key(self):
        """Test analysis of weak private key."""
        # Create a key with known weakness (all zeros)
        weak_key = bytes(32)
        
        vulnerabilities = self.analyzer.analyze_key_security(weak_key)
        
        # Should detect vulnerabilities
        self.assertGreater(len(vulnerabilities), 0)
        
        # Check vulnerability types
        vuln_types = [v.vulnerability_type for v in vulnerabilities]
        self.assertIn(VulnerabilityType.KNOWN_WEAK_KEY, vuln_types)
    
    def test_analyze_patterned_key(self):
        """Test analysis of key with patterns."""
        # Create a key with repeated pattern
        patterned_key = bytes([0x01, 0x02, 0x03, 0x04] * 8)
        
        vulnerabilities = self.analyzer.analyze_key_security(patterned_key)
        
        # Should detect pattern-related vulnerabilities
        self.assertGreater(len(vulnerabilities), 0)
    
    def test_batch_analysis(self):
        """Test batch analysis functionality."""
        import secrets
        
        # Mix of secure and weak keys
        keys = [
            secrets.token_bytes(32),  # Secure
            bytes(32),                # Weak (all zeros)
            secrets.token_bytes(32),  # Secure
            bytes([0x01] * 32)        # Weak (repeated pattern)
        ]
        
        result = self.analyzer.perform_batch_analysis(keys)
        
        self.assertEqual(result.total_keys_analyzed, 4)
        self.assertEqual(result.secure_keys, 2)
        self.assertEqual(result.vulnerable_keys, 2)
        self.assertGreater(len(result.vulnerabilities_found), 0)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        # Create temporary config file
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, 'test_config.yaml')
        
        test_config = {
            'cryptography': {
                'entropy_sources': ['system'],
                'min_entropy_threshold': 6.0
            },
            'performance': {
                'max_concurrent_batches': 2,
                'batch_size': 10
            },
            'data': {
                'base_directory': self.temp_dir,
                'file_permissions': '600'
            },
            'security_research': {
                'min_entropy_threshold': 6.0,
                'pattern_detection': True
            },
            'compliance': {
                'require_acknowledgment': False,
                'max_daily_generations': 1000
            }
        }
        
        with open(self.config_path, 'w') as f:
            yaml.dump(test_config, f)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    async def test_end_to_end_wallet_generation(self):
        """Test complete end-to-end wallet generation process."""
        # This would test the full enhanced_wallet_generator workflow
        # For now, we'll test key components integration
        
        # Initialize components
        from crypto_security import SecureRandomGenerator
        from cryptocurrency import CryptocurrencyFactory, CryptocurrencyType
        
        generator = SecureRandomGenerator({'entropy_sources': ['system']})
        bitcoin = CryptocurrencyFactory.create_cryptocurrency(CryptocurrencyType.BITCOIN)
        
        # Generate and validate a wallet
        private_key, metrics = generator.generate_private_key()
        wallet = bitcoin.create_wallet(private_key)
        
        # Verify wallet structure
        self.assertEqual(len(wallet.private_key_hex), 64)
        self.assertGreater(len(wallet.addresses), 0)
        
        # Verify address format
        for address_info in wallet.addresses:
            self.assertIsInstance(address_info.address, str)
            self.assertGreater(len(address_info.address), 20)


def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestSecureRandomGenerator,
        TestKeyValidator,
        TestCryptocurrencyFactory,
        TestBatchProcessor,
        TestProgressTracker,
        TestSchemaValidator,
        TestVulnerabilityAnalyzer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    # Run tests
    success = run_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
