#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد محافظ البلوك تشين المتقدم مع دعم Bitcoin و Ethereum
Advanced Blockchain Wallet Generator with Bitcoin and Ethereum support
"""

import os
import secrets
import hashlib
import logging
import time
import requests
import json
from datetime import datetime
from typing import Tuple, Optional, Dict
import threading
from concurrent.futures import ThreadPoolExecutor
import base58

# إعداد نظام التسجيل المتقدم
class ColoredFormatter(logging.Formatter):
    """مُنسق الألوان للوجر"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # سماوي
        'INFO': '\033[32m',     # أخضر
        'WARNING': '\033[33m',  # أصفر
        'ERROR': '\033[31m',    # أحمر
        'CRITICAL': '\033[35m', # بنفسجي
        'RESET': '\033[0m'      # إعادة تعيين
    }
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)

# إعداد اللوجر
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# إعداد معالج الملف
file_handler = logging.FileHandler('advanced_wallet_generator.log', encoding='utf-8')
file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# إعداد معالج وحدة التحكم
console_handler = logging.StreamHandler()
console_formatter = ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(file_handler)
logger.addHandler(console_handler)

class CryptoUtils:
    """أدوات التشفير للبلوك تشين"""
    
    @staticmethod
    def generate_private_key() -> str:
        """إنشاء مفتاح خاص عشوائي آمن"""
        return secrets.randbits(256).to_bytes(32, byteorder='big').hex()
    
    @staticmethod
    def private_key_to_wif(private_key_hex: str, compressed: bool = True) -> str:
        """تحويل المفتاح الخاص إلى تنسيق WIF"""
        private_key_bytes = bytes.fromhex(private_key_hex)
        
        # إضافة version byte (0x80 for mainnet)
        extended_key = b'\x80' + private_key_bytes
        
        # إضافة compression flag إذا كان مضغوطاً
        if compressed:
            extended_key += b'\x01'
        
        # حساب checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # الترميز النهائي
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode()

class BitcoinWallet:
    """فئة محفظة Bitcoin"""
    
    def __init__(self, private_key: str = None):
        self.private_key = private_key or CryptoUtils.generate_private_key()
        self.public_key = self._generate_public_key()
        self.address = self._generate_address()
        self.wif = CryptoUtils.private_key_to_wif(self.private_key)
    
    def _generate_public_key(self) -> str:
        """إنشاء المفتاح العام (مبسط)"""
        # في التطبيق الحقيقي، ستحتاج إلى مكتبة secp256k1
        private_key_bytes = bytes.fromhex(self.private_key)
        public_key_hash = hashlib.sha256(private_key_bytes).hexdigest()
        return public_key_hash
    
    def _generate_address(self) -> str:
        """إنشاء عنوان Bitcoin"""
        public_key_bytes = bytes.fromhex(self.public_key)
        
        # SHA256 hash
        sha256_hash = hashlib.sha256(public_key_bytes).digest()
        
        # RIPEMD160 hash
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # إضافة version byte
        versioned_hash = b'\x00' + hash160
        
        # حساب checksum
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # العنوان النهائي
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode()

class EthereumWallet:
    """فئة محفظة Ethereum"""
    
    def __init__(self, private_key: str = None):
        self.private_key = private_key or CryptoUtils.generate_private_key()
        self.public_key = self._generate_public_key()
        self.address = self._generate_address()
    
    def _generate_public_key(self) -> str:
        """إنشاء المفتاح العام لـ Ethereum"""
        private_key_bytes = bytes.fromhex(self.private_key)
        public_key_hash = hashlib.sha256(private_key_bytes).hexdigest()
        return public_key_hash
    
    def _generate_address(self) -> str:
        """إنشاء عنوان Ethereum"""
        public_key_bytes = bytes.fromhex(self.public_key)
        keccak_hash = hashlib.sha3_256(public_key_bytes).hexdigest()
        address = "0x" + keccak_hash[-40:]
        return address

class BalanceChecker:
    """فاحص الأرصدة للعملات المختلفة"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # معدل الطلبات (requests per second)
        self.rate_limit = 1.0
        self.last_request_time = 0
    
    def _rate_limit_wait(self):
        """انتظار للحد من معدل الطلبات"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit:
            time.sleep(self.rate_limit - time_since_last)
        self.last_request_time = time.time()
    
    def check_bitcoin_balance(self, address: str) -> Optional[Dict]:
        """فحص رصيد Bitcoin"""
        self._rate_limit_wait()
        
        try:
            # استخدام BlockCypher API
            url = f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance"
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('balance', 0)
                total_received_satoshi = data.get('total_received', 0)
                
                return {
                    'balance_btc': balance_satoshi / 100000000,
                    'balance_satoshi': balance_satoshi,
                    'total_received_btc': total_received_satoshi / 100000000,
                    'total_received_satoshi': total_received_satoshi,
                    'tx_count': data.get('n_tx', 0)
                }
            else:
                logger.warning(f"⚠️ خطأ API Bitcoin: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ فحص Bitcoin: {e}")
            return None
    
    def check_ethereum_balance(self, address: str) -> Optional[Dict]:
        """فحص رصيد Ethereum (يتطلب API key)"""
        # يمكن إضافة Etherscan API هنا
        return {'balance_eth': 0.0, 'tx_count': 0}

class WalletGenerator:
    """مولد المحافظ الرئيسي"""
    
    def __init__(self, max_workers: int = 5):
        self.balance_checker = BalanceChecker()
        self.max_workers = max_workers
        
        # إحصائيات
        self.stats = {
            'total_generated': 0,
            'bitcoin_with_balance': 0,
            'ethereum_with_balance': 0,
            'total_btc_found': 0.0,
            'total_eth_found': 0.0,
            'start_time': None
        }
        
        # قفل للإحصائيات
        self.stats_lock = threading.Lock()
        
        logger.info("🚀 تم تشغيل مولد المحافظ المتقدم")
        logger.info("=" * 70)
    
    def save_wallet_data(self, wallet_type: str, wallet_data: Dict):
        """حفظ بيانات المحفظة"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        data = {
            'timestamp': timestamp,
            'type': wallet_type,
            **wallet_data
        }
        
        filename = f"found_wallets_{wallet_type.lower()}.json"
        
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    wallets = json.load(f)
            else:
                wallets = []
            
            wallets.append(data)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(wallets, f, indent=2, ensure_ascii=False)
                
            logger.info(f"💾 تم حفظ محفظة {wallet_type} في {filename}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ المحفظة: {e}")
    
    def process_bitcoin_wallet(self, iteration: int) -> bool:
        """معالجة محفظة Bitcoin واحدة"""
        try:
            wallet = BitcoinWallet()
            
            logger.info(f"🔑 Bitcoin #{iteration}")
            logger.info(f"   العنوان: {wallet.address}")
            
            balance_info = self.balance_checker.check_bitcoin_balance(wallet.address)
            
            if balance_info:
                balance = balance_info['balance_btc']
                total_received = balance_info['total_received_btc']
                
                if balance > 0 or total_received > 0:
                    with self.stats_lock:
                        self.stats['bitcoin_with_balance'] += 1
                        self.stats['total_btc_found'] += balance
                    
                    logger.info(f"💰 تم العثور على رصيد Bitcoin!")
                    logger.info(f"   الرصيد الحالي: {balance} BTC")
                    logger.info(f"   إجمالي المستلم: {total_received} BTC")
                    logger.info(f"   عدد المعاملات: {balance_info['tx_count']}")
                    logger.info(f"   المفتاح الخاص: {wallet.private_key}")
                    logger.info(f"   WIF: {wallet.wif}")
                    
                    # حفظ المحفظة
                    wallet_data = {
                        'private_key': wallet.private_key,
                        'wif': wallet.wif,
                        'address': wallet.address,
                        'balance_btc': balance,
                        'total_received_btc': total_received,
                        'tx_count': balance_info['tx_count']
                    }
                    self.save_wallet_data('Bitcoin', wallet_data)
                    return True
                else:
                    logger.info(f"💸 رصيد فارغ: {balance} BTC")
            else:
                logger.info("❓ لم يتم التمكن من فحص الرصيد")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة Bitcoin #{iteration}: {e}")
            return False
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        with self.stats_lock:
            self.stats['total_generated'] += 1
    
    def print_progress(self, iteration: int):
        """طباعة تقرير التقدم"""
        if iteration % 10 == 0:
            with self.stats_lock:
                elapsed_time = time.time() - self.stats['start_time']
                rate = self.stats['total_generated'] / elapsed_time if elapsed_time > 0 else 0
                
                logger.info("📊 تقرير التقدم:")
                logger.info(f"   المحافظ المُنشأة: {self.stats['total_generated']}")
                logger.info(f"   Bitcoin بأرصدة: {self.stats['bitcoin_with_balance']}")
                logger.info(f"   إجمالي BTC: {self.stats['total_btc_found']:.8f}")
                logger.info(f"   المعدل: {rate:.2f} محفظة/ثانية")
                logger.info(f"   الوقت المنقضي: {elapsed_time:.1f} ثانية")
                logger.info("-" * 50)
    
    def run(self, max_iterations: int = 1000, delay: float = 1.0):
        """تشغيل مولد المحافظ"""
        self.stats['start_time'] = time.time()
        
        logger.info(f"🎯 بدء البحث - العدد الأقصى: {max_iterations}")
        logger.info(f"⏱️ التأخير: {delay} ثانية")
        logger.info(f"🔧 عدد الخيوط: {self.max_workers}")
        logger.info("=" * 70)
        
        try:
            for i in range(max_iterations):
                self.process_bitcoin_wallet(i + 1)
                self.update_stats()
                self.print_progress(i + 1)
                
                time.sleep(delay)
                
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        
        # تقرير نهائي
        self.print_final_report()
    
    def print_final_report(self):
        """طباعة التقرير النهائي"""
        total_time = time.time() - self.stats['start_time']
        
        logger.info("🏁 انتهى البحث!")
        logger.info("=" * 70)
        logger.info("📈 التقرير النهائي:")
        logger.info(f"   إجمالي المحافظ: {self.stats['total_generated']}")
        logger.info(f"   Bitcoin بأرصدة: {self.stats['bitcoin_with_balance']}")
        logger.info(f"   إجمالي BTC مكتشف: {self.stats['total_btc_found']:.8f}")
        logger.info(f"   الوقت الإجمالي: {total_time:.2f} ثانية")
        logger.info(f"   المعدل النهائي: {self.stats['total_generated']/total_time:.2f} محفظة/ثانية")
        
        if self.stats['bitcoin_with_balance'] > 0:
            logger.info(f"🎉 تم العثور على {self.stats['bitcoin_with_balance']} محفظة بأرصدة!")

def main():
    """الدالة الرئيسية"""
    print("🌟 مرحباً بك في مولد المحافظ المتقدم")
    print("=" * 60)
    
    try:
        max_iterations = int(input("عدد المحافظ (افتراضي: 100): ") or "100")
        delay = float(input("التأخير بالثواني (افتراضي: 1.0): ") or "1.0")
        max_workers = int(input("عدد الخيوط (افتراضي: 1): ") or "1")
        
        generator = WalletGenerator(max_workers)
        generator.run(max_iterations, delay)
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        logger.error(f"❌ خطأ في البرنامج: {e}")

if __name__ == "__main__":
    main()
