#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإنشاء مفاتيح البلوك تشين العشوائية والتحقق من أرصدة المحافظ
Blockchain Wallet Generator and Balance Checker
"""

import os
import secrets
import hashlib
import logging
import time
import requests
from typing import Tuple, Optional
import json
from datetime import datetime

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wallet_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BlockchainWalletGenerator:
    """مولد محافظ البلوك تشين مع فحص الأرصدة"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # عدادات الإحصائيات
        self.total_generated = 0
        self.wallets_with_balance = 0
        self.total_balance_found = 0.0
        
        logger.info("🚀 تم تشغيل مولد محافظ البلوك تشين")
        logger.info("=" * 60)
    
    def generate_private_key(self) -> str:
        """إنشاء مفتاح خاص عشوائي 256-bit"""
        private_key_bytes = secrets.randbits(256).to_bytes(32, byteorder='big')
        private_key_hex = private_key_bytes.hex()
        return private_key_hex
    
    def private_key_to_public_key(self, private_key: str) -> str:
        """تحويل المفتاح الخاص إلى مفتاح عام (مبسط)"""
        # هذا تنفيذ مبسط - في الواقع يتطلب مكتبة secp256k1
        private_key_bytes = bytes.fromhex(private_key)
        public_key_hash = hashlib.sha256(private_key_bytes).hexdigest()
        return public_key_hash
    
    def public_key_to_address(self, public_key: str) -> str:
        """تحويل المفتاح العام إلى عنوان محفظة Bitcoin"""
        # تنفيذ مبسط لإنشاء عنوان Bitcoin
        public_key_bytes = bytes.fromhex(public_key)
        
        # SHA256 hash
        sha256_hash = hashlib.sha256(public_key_bytes).digest()
        
        # RIPEMD160 hash
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # إضافة version byte (0x00 for mainnet)
        versioned_hash = b'\x00' + hash160
        
        # Double SHA256 for checksum
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # العنوان النهائي
        address_bytes = versioned_hash + checksum
        
        # تحويل إلى Base58 (مبسط)
        address = self.base58_encode(address_bytes)
        return address
    
    def base58_encode(self, data: bytes) -> str:
        """ترميز Base58 مبسط"""
        alphabet = "**********************************************************"
        num = int.from_bytes(data, 'big')
        
        if num == 0:
            return alphabet[0]
        
        result = ""
        while num > 0:
            num, remainder = divmod(num, 58)
            result = alphabet[remainder] + result
        
        # إضافة الأصفار البادئة
        for byte in data:
            if byte == 0:
                result = alphabet[0] + result
            else:
                break
        
        return result
    
    def check_bitcoin_balance(self, address: str) -> Optional[float]:
        """فحص رصيد محفظة Bitcoin"""
        try:
            # استخدام BlockCypher API
            url = f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('balance', 0)
                balance_btc = balance_satoshi / 100000000  # تحويل من satoshi إلى BTC
                return balance_btc
            else:
                logger.warning(f"⚠️ خطأ في API: {response.status_code} للعنوان {address}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ خطأ في الشبكة: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع: {e}")
            return None
    
    def check_ethereum_balance(self, address: str) -> Optional[float]:
        """فحص رصيد محفظة Ethereum (مبسط)"""
        try:
            # يمكن استخدام Etherscan API هنا
            # هذا مثال مبسط
            logger.info(f"🔍 فحص رصيد Ethereum للعنوان: {address}")
            # في التطبيق الحقيقي، ستحتاج إلى API key من Etherscan
            return 0.0
        except Exception as e:
            logger.error(f"❌ خطأ في فحص Ethereum: {e}")
            return None
    
    def generate_wallet(self) -> Tuple[str, str, str]:
        """إنشاء محفظة كاملة (مفتاح خاص، مفتاح عام، عنوان)"""
        private_key = self.generate_private_key()
        public_key = self.private_key_to_public_key(private_key)
        address = self.public_key_to_address(public_key)
        
        return private_key, public_key, address
    
    def save_wallet_with_balance(self, private_key: str, address: str, balance: float):
        """حفظ المحافظ التي تحتوي على رصيد"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        wallet_data = {
            "timestamp": timestamp,
            "private_key": private_key,
            "address": address,
            "balance_btc": balance
        }
        
        # حفظ في ملف JSON
        filename = "wallets_with_balance.json"
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    wallets = json.load(f)
            else:
                wallets = []
            
            wallets.append(wallet_data)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(wallets, f, indent=2, ensure_ascii=False)
                
            logger.info(f"💾 تم حفظ المحفظة في {filename}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ المحفظة: {e}")
    
    def run_generator(self, max_iterations: int = 1000, delay: float = 1.0):
        """تشغيل مولد المحافظ"""
        logger.info(f"🎯 بدء البحث عن المحافظ - العدد الأقصى: {max_iterations}")
        logger.info(f"⏱️ التأخير بين كل محاولة: {delay} ثانية")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        for i in range(max_iterations):
            try:
                # إنشاء محفظة جديدة
                private_key, public_key, address = self.generate_wallet()
                self.total_generated += 1
                
                logger.info(f"🔑 المحفظة #{i+1}")
                logger.info(f"   المفتاح الخاص: {private_key}")
                logger.info(f"   العنوان: {address}")
                
                # فحص الرصيد
                balance = self.check_bitcoin_balance(address)
                
                if balance is not None:
                    if balance > 0:
                        self.wallets_with_balance += 1
                        self.total_balance_found += balance
                        
                        logger.info(f"💰 تم العثور على رصيد! {balance} BTC")
                        logger.info(f"🎉 المفتاح الخاص: {private_key}")
                        logger.info(f"🎉 العنوان: {address}")
                        logger.info("=" * 60)
                        
                        # حفظ المحفظة
                        self.save_wallet_with_balance(private_key, address, balance)
                        
                    else:
                        logger.info(f"💸 رصيد فارغ: {balance} BTC")
                else:
                    logger.info("❓ لم يتم التمكن من فحص الرصيد")
                
                # عرض الإحصائيات كل 10 محاولات
                if (i + 1) % 10 == 0:
                    elapsed_time = time.time() - start_time
                    rate = (i + 1) / elapsed_time
                    
                    logger.info("📊 الإحصائيات:")
                    logger.info(f"   المحافظ المُنشأة: {self.total_generated}")
                    logger.info(f"   المحافظ بأرصدة: {self.wallets_with_balance}")
                    logger.info(f"   إجمالي الأرصدة: {self.total_balance_found} BTC")
                    logger.info(f"   المعدل: {rate:.2f} محفظة/ثانية")
                    logger.info("-" * 40)
                
                # تأخير بين المحاولات
                time.sleep(delay)
                
            except KeyboardInterrupt:
                logger.info("⏹️ تم إيقاف البرنامج بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"❌ خطأ في المحاولة #{i+1}: {e}")
                continue
        
        # إحصائيات نهائية
        total_time = time.time() - start_time
        logger.info("🏁 انتهى البحث!")
        logger.info("=" * 60)
        logger.info("📈 الإحصائيات النهائية:")
        logger.info(f"   إجمالي المحافظ المُنشأة: {self.total_generated}")
        logger.info(f"   المحافظ بأرصدة: {self.wallets_with_balance}")
        logger.info(f"   إجمالي الأرصدة المكتشفة: {self.total_balance_found} BTC")
        logger.info(f"   الوقت الإجمالي: {total_time:.2f} ثانية")
        logger.info(f"   المعدل النهائي: {self.total_generated/total_time:.2f} محفظة/ثانية")

def main():
    """الدالة الرئيسية"""
    print("🌟 مرحباً بك في مولد محافظ البلوك تشين")
    print("=" * 50)
    
    generator = BlockchainWalletGenerator()
    
    try:
        # يمكنك تعديل هذه القيم
        max_iterations = int(input("أدخل عدد المحافظ المراد إنشاؤها (افتراضي: 100): ") or "100")
        delay = float(input("أدخل التأخير بالثواني بين كل محاولة (افتراضي: 1.0): ") or "1.0")
        
        generator.run_generator(max_iterations, delay)
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        logger.error(f"❌ خطأ في البرنامج الرئيسي: {e}")

if __name__ == "__main__":
    main()
