#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أمثلة على استخدام مولد محافظ البلوك تشين
Examples of using the blockchain wallet generator
"""

import json
import time
from blockchain_wallet_generator import BlockchainWalletGenerator
from advanced_wallet_generator import WalletGenerator, BitcoinWallet

def example_basic_usage():
    """مثال على الاستخدام الأساسي"""
    print("🔥 مثال على الاستخدام الأساسي")
    print("=" * 40)
    
    generator = BlockchainWalletGenerator()
    
    # إنشاء محفظة واحدة
    private_key, public_key, address = generator.generate_wallet()
    
    print(f"المفتاح الخاص: {private_key}")
    print(f"المفتاح العام: {public_key}")
    print(f"العنوان: {address}")
    
    # فحص الرصيد
    balance = generator.check_bitcoin_balance(address)
    if balance is not None:
        print(f"الرصيد: {balance} BTC")
    else:
        print("لم يتم التمكن من فحص الرصيد")

def example_advanced_usage():
    """مثال على الاستخدام المتقدم"""
    print("\n🚀 مثال على الاستخدام المتقدم")
    print("=" * 40)
    
    # إنشاء محفظة Bitcoin متقدمة
    wallet = BitcoinWallet()
    
    print(f"المفتاح الخاص: {wallet.private_key}")
    print(f"WIF: {wallet.wif}")
    print(f"العنوان: {wallet.address}")

def example_batch_generation():
    """مثال على الإنشاء المجمع"""
    print("\n📦 مثال على الإنشاء المجمع")
    print("=" * 40)
    
    generator = BlockchainWalletGenerator()
    wallets = []
    
    # إنشاء 5 محافظ
    for i in range(5):
        private_key, public_key, address = generator.generate_wallet()
        wallets.append({
            'id': i + 1,
            'private_key': private_key,
            'address': address
        })
        print(f"محفظة #{i+1}: {address}")
    
    # حفظ في ملف
    with open('example_wallets.json', 'w', encoding='utf-8') as f:
        json.dump(wallets, f, indent=2, ensure_ascii=False)
    
    print("تم حفظ المحافظ في example_wallets.json")

def example_config_usage():
    """مثال على استخدام ملف التكوين"""
    print("\n⚙️ مثال على استخدام ملف التكوين")
    print("=" * 40)
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("إعدادات افتراضية:")
        for key, value in config['default_settings'].items():
            print(f"  {key}: {value}")
        
        print("\nإعدادات API:")
        for key, value in config['api_settings'].items():
            print(f"  {key}: {value}")
            
    except FileNotFoundError:
        print("ملف التكوين غير موجود")

def example_statistics():
    """مثال على عرض الإحصائيات"""
    print("\n📊 مثال على الإحصائيات")
    print("=" * 40)
    
    # إحصائيات وهمية للمثال
    stats = {
        'total_generated': 1000,
        'wallets_with_balance': 0,
        'total_balance_found': 0.0,
        'runtime_seconds': 3600,
        'rate_per_second': 0.28
    }
    
    print(f"إجمالي المحافظ المُنشأة: {stats['total_generated']:,}")
    print(f"المحافظ بأرصدة: {stats['wallets_with_balance']}")
    print(f"إجمالي الأرصدة: {stats['total_balance_found']} BTC")
    print(f"وقت التشغيل: {stats['runtime_seconds']/3600:.1f} ساعة")
    print(f"المعدل: {stats['rate_per_second']:.2f} محفظة/ثانية")
    
    # حساب الاحتمالات
    probability = 1 / (2**160)  # احتمالية العثور على محفظة بأرصدة
    expected_attempts = 2**160  # العدد المتوقع من المحاولات
    
    print(f"\nالاحتمالات النظرية:")
    print(f"احتمالية العثور على محفظة: 1 في {expected_attempts:.2e}")
    print(f"الوقت المتوقع (بمعدل حالي): {expected_attempts/stats['rate_per_second']/31536000:.2e} سنة")

def main():
    """الدالة الرئيسية للأمثلة"""
    print("🌟 أمثلة على استخدام مولد محافظ البلوك تشين")
    print("=" * 60)
    
    try:
        example_basic_usage()
        time.sleep(1)
        
        example_advanced_usage()
        time.sleep(1)
        
        example_batch_generation()
        time.sleep(1)
        
        example_config_usage()
        time.sleep(1)
        
        example_statistics()
        
        print("\n✅ تم تشغيل جميع الأمثلة بنجاح!")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الأمثلة")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الأمثلة: {e}")

if __name__ == "__main__":
    main()
