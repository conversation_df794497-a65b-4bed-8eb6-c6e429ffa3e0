"""
Enhanced Cryptographic Security Module

This module implements industry-standard secure random number generation,
entropy collection, and cryptographic validation for educational and
security research purposes.

Author: Security Research Team
License: Educational Use Only
"""

import os
import secrets
import hashlib
import hmac
import time
from typing import Optional, List, Dict, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
from Crypto.Random import get_random_bytes
from Crypto.Hash import SHA256, RIPEMD160

logger = logging.getLogger(__name__)


class EntropySource(Enum):
    """Enumeration of available entropy sources."""
    OS_URANDOM = "os.urandom"
    SECRETS = "secrets"
    CRYPTOGRAPHY = "cryptography.hazmat"
    HARDWARE_RNG = "hardware_rng"


class KeyValidationResult(Enum):
    """Key validation results."""
    VALID = "valid"
    WEAK_ENTROPY = "weak_entropy"
    SEQUENTIAL_PATTERN = "sequential_pattern"
    REPEATED_PATTERN = "repeated_pattern"
    KNOWN_WEAK = "known_weak"
    INVALID_FORMAT = "invalid_format"


@dataclass
class EntropyAnalysis:
    """Results of entropy analysis."""
    entropy_bits: float
    is_sufficient: bool
    patterns_detected: List[str]
    source_quality: str
    recommendations: List[str]


@dataclass
class KeyGenerationMetrics:
    """Metrics for key generation process."""
    generation_time_ms: float
    entropy_collected_bits: int
    validation_passed: bool
    source_used: str
    timestamp: float


class SecureRandomGenerator:
    """
    Industry-standard secure random number generator with multiple entropy sources.
    
    This class implements cryptographically secure pseudorandom number generation
    with proper entropy collection, validation, and security measures.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the secure random generator.
        
        Args:
            config: Configuration dictionary containing security settings
        """
        self.config = config
        self.entropy_pool = bytearray()
        self.reseed_counter = 0
        self.last_reseed_time = time.time()
        self.minimum_entropy_bits = config.get('minimum_entropy_bits', 256)
        self.entropy_pool_size = config.get('entropy_pool_size', 4096)
        self.reseed_interval = config.get('reseed_interval', 1000)
        
        # Initialize entropy sources
        self.available_sources = self._initialize_entropy_sources()
        
        # Perform initial entropy collection
        self._collect_initial_entropy()
        
        logger.info(f"SecureRandomGenerator initialized with {len(self.available_sources)} entropy sources")
    
    def _initialize_entropy_sources(self) -> List[EntropySource]:
        """Initialize available entropy sources based on configuration."""
        sources = []
        
        # Always available sources
        sources.extend([EntropySource.OS_URANDOM, EntropySource.SECRETS])
        
        # Check for cryptography module
        try:
            from cryptography.hazmat.primitives import hashes
            sources.append(EntropySource.CRYPTOGRAPHY)
        except ImportError:
            logger.warning("Cryptography module not available")
        
        # Check for hardware RNG (if configured)
        if self.config.get('use_hardware_rng', False):
            if self._test_hardware_rng():
                sources.append(EntropySource.HARDWARE_RNG)
            else:
                logger.warning("Hardware RNG requested but not available")
        
        return sources
    
    def _test_hardware_rng(self) -> bool:
        """Test if hardware RNG is available."""
        try:
            # This is a placeholder - actual implementation would depend on hardware
            # For educational purposes, we'll assume it's not available
            return False
        except Exception:
            return False
    
    def _collect_initial_entropy(self) -> None:
        """Collect initial entropy from all available sources."""
        logger.debug("Collecting initial entropy")
        
        for source in self.available_sources:
            try:
                entropy_data = self._collect_entropy_from_source(source, 64)
                self.entropy_pool.extend(entropy_data)
            except Exception as e:
                logger.warning(f"Failed to collect entropy from {source}: {e}")
        
        # Ensure minimum entropy pool size
        while len(self.entropy_pool) < self.entropy_pool_size:
            additional_entropy = self._collect_entropy_from_source(
                EntropySource.OS_URANDOM, 64
            )
            self.entropy_pool.extend(additional_entropy)
        
        # Limit pool size
        if len(self.entropy_pool) > self.entropy_pool_size:
            self.entropy_pool = self.entropy_pool[:self.entropy_pool_size]
        
        logger.info(f"Initial entropy pool collected: {len(self.entropy_pool)} bytes")
    
    def _collect_entropy_from_source(self, source: EntropySource, size: int) -> bytes:
        """Collect entropy from a specific source."""
        if source == EntropySource.OS_URANDOM:
            return os.urandom(size)
        elif source == EntropySource.SECRETS:
            return secrets.token_bytes(size)
        elif source == EntropySource.CRYPTOGRAPHY:
            return get_random_bytes(size)
        elif source == EntropySource.HARDWARE_RNG:
            # Placeholder for hardware RNG implementation
            return os.urandom(size)  # Fallback to OS urandom
        else:
            raise ValueError(f"Unknown entropy source: {source}")
    
    def _should_reseed(self) -> bool:
        """Determine if reseeding is necessary."""
        return (
            self.reseed_counter >= self.reseed_interval or
            time.time() - self.last_reseed_time > 3600  # Reseed every hour
        )
    
    def _reseed_entropy_pool(self) -> None:
        """Reseed the entropy pool with fresh entropy."""
        logger.debug("Reseeding entropy pool")
        
        # Collect fresh entropy
        fresh_entropy = bytearray()
        for source in self.available_sources:
            try:
                entropy_data = self._collect_entropy_from_source(source, 32)
                fresh_entropy.extend(entropy_data)
            except Exception as e:
                logger.warning(f"Failed to collect entropy during reseed from {source}: {e}")
        
        # Mix with existing pool using cryptographic hash
        combined = bytes(self.entropy_pool) + bytes(fresh_entropy)
        new_pool = hashlib.sha512(combined).digest()
        
        # Extend to desired pool size
        while len(new_pool) < self.entropy_pool_size:
            new_pool += hashlib.sha512(new_pool).digest()
        
        self.entropy_pool = bytearray(new_pool[:self.entropy_pool_size])
        self.reseed_counter = 0
        self.last_reseed_time = time.time()
        
        logger.debug("Entropy pool reseeded successfully")
    
    def generate_secure_bytes(self, size: int) -> bytes:
        """
        Generate cryptographically secure random bytes.
        
        Args:
            size: Number of bytes to generate
            
        Returns:
            Cryptographically secure random bytes
        """
        if size <= 0:
            raise ValueError("Size must be positive")
        
        # Check if reseeding is needed
        if self._should_reseed():
            self._reseed_entropy_pool()
        
        # Generate random bytes using multiple sources
        start_time = time.time()
        
        # Primary generation using secrets module
        primary_bytes = secrets.token_bytes(size)
        
        # Secondary generation using entropy pool
        pool_hash = hashlib.sha256(bytes(self.entropy_pool)).digest()
        secondary_bytes = hashlib.pbkdf2_hmac(
            'sha256', 
            primary_bytes, 
            pool_hash, 
            100000,  # iterations
            size
        )
        
        # Combine using XOR for additional security
        result = bytes(a ^ b for a, b in zip(primary_bytes, secondary_bytes))
        
        # Update counters
        self.reseed_counter += 1
        generation_time = (time.time() - start_time) * 1000
        
        logger.debug(f"Generated {size} secure bytes in {generation_time:.2f}ms")
        
        return result
    
    def generate_private_key(self) -> Tuple[bytes, KeyGenerationMetrics]:
        """
        Generate a cryptographically secure private key.
        
        Returns:
            Tuple of (private_key_bytes, generation_metrics)
        """
        start_time = time.time()
        
        # Generate 32 bytes (256 bits) for private key
        private_key_bytes = self.generate_secure_bytes(32)
        
        # Ensure the key is within valid range for secp256k1
        # Private key must be between 1 and n-1 where n is the curve order
        secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
        private_key_int = int.from_bytes(private_key_bytes, 'big')
        
        # Regenerate if key is invalid (extremely rare)
        while private_key_int == 0 or private_key_int >= secp256k1_order:
            logger.warning("Generated invalid private key, regenerating")
            private_key_bytes = self.generate_secure_bytes(32)
            private_key_int = int.from_bytes(private_key_bytes, 'big')
        
        generation_time = (time.time() - start_time) * 1000
        
        metrics = KeyGenerationMetrics(
            generation_time_ms=generation_time,
            entropy_collected_bits=256,
            validation_passed=True,
            source_used="multi_source_csprng",
            timestamp=time.time()
        )
        
        logger.info(f"Private key generated successfully in {generation_time:.2f}ms")
        
        return private_key_bytes, metrics


class EntropyAnalyzer:
    """
    Analyzer for detecting weak entropy patterns and validating randomness quality.
    
    This class implements various statistical tests and pattern detection algorithms
    to identify potential weaknesses in generated keys for educational purposes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the entropy analyzer."""
        self.config = config
        self.weak_patterns = self._load_weak_patterns()
        
    def _load_weak_patterns(self) -> Dict[str, List[bytes]]:
        """Load known weak patterns for detection."""
        patterns = {
            'sequential': [],
            'repeated': [],
            'known_weak': []
        }
        
        # Sequential patterns (for educational demonstration)
        for i in range(256):
            pattern = bytes([(i + j) % 256 for j in range(32)])
            patterns['sequential'].append(pattern)
        
        # Repeated byte patterns
        for byte_val in [0x00, 0xFF, 0xAA, 0x55]:
            pattern = bytes([byte_val] * 32)
            patterns['repeated'].append(pattern)
        
        # Known weak keys from research (educational examples)
        # These are well-documented weak keys for demonstration
        patterns['known_weak'].extend([
            bytes.fromhex('0000000000000000000000000000000000000000000000000000000000000001'),
            bytes.fromhex('FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140'),
        ])
        
        return patterns
    
    def analyze_entropy(self, data: bytes) -> EntropyAnalysis:
        """
        Analyze the entropy quality of given data.
        
        Args:
            data: Bytes to analyze
            
        Returns:
            EntropyAnalysis object with results
        """
        if len(data) == 0:
            return EntropyAnalysis(
                entropy_bits=0.0,
                is_sufficient=False,
                patterns_detected=['empty_data'],
                source_quality='invalid',
                recommendations=['Provide non-empty data']
            )
        
        # Calculate Shannon entropy
        entropy_bits = self._calculate_shannon_entropy(data)
        
        # Detect patterns
        patterns_detected = self._detect_patterns(data)
        
        # Assess overall quality
        is_sufficient = (
            entropy_bits >= self.config.get('minimum_entropy_bits', 7.0) and
            len(patterns_detected) == 0
        )
        
        # Generate recommendations
        recommendations = self._generate_recommendations(entropy_bits, patterns_detected)
        
        # Determine source quality
        if entropy_bits >= 7.5 and len(patterns_detected) == 0:
            source_quality = 'excellent'
        elif entropy_bits >= 7.0 and len(patterns_detected) <= 1:
            source_quality = 'good'
        elif entropy_bits >= 6.0:
            source_quality = 'acceptable'
        else:
            source_quality = 'poor'
        
        return EntropyAnalysis(
            entropy_bits=entropy_bits,
            is_sufficient=is_sufficient,
            patterns_detected=patterns_detected,
            source_quality=source_quality,
            recommendations=recommendations
        )
    
    def _calculate_shannon_entropy(self, data: bytes) -> float:
        """Calculate Shannon entropy of the data."""
        if len(data) == 0:
            return 0.0
        
        # Count byte frequencies
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # Calculate entropy
        entropy = 0.0
        data_len = len(data)
        
        for count in byte_counts:
            if count > 0:
                probability = count / data_len
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def _detect_patterns(self, data: bytes) -> List[str]:
        """Detect known weak patterns in the data."""
        patterns_found = []
        
        # Check for repeated bytes
        if len(set(data)) == 1:
            patterns_found.append('all_same_byte')
        
        # Check for sequential patterns
        if self._is_sequential(data):
            patterns_found.append('sequential')
        
        # Check for known weak patterns
        for pattern_type, patterns in self.weak_patterns.items():
            if data in patterns:
                patterns_found.append(f'known_weak_{pattern_type}')
        
        # Check for low complexity
        if len(set(data)) < len(data) * 0.1:
            patterns_found.append('low_complexity')
        
        return patterns_found
    
    def _is_sequential(self, data: bytes) -> bool:
        """Check if data contains sequential patterns."""
        if len(data) < 4:
            return False
        
        # Check for ascending sequences
        ascending_count = 0
        for i in range(len(data) - 1):
            if (data[i + 1] - data[i]) % 256 == 1:
                ascending_count += 1
            else:
                ascending_count = 0
            
            if ascending_count >= 8:  # 8 consecutive ascending bytes
                return True
        
        return False
    
    def _generate_recommendations(self, entropy: float, patterns: List[str]) -> List[str]:
        """Generate recommendations based on analysis results."""
        recommendations = []
        
        if entropy < 6.0:
            recommendations.append("Entropy is too low - use a better random source")
        
        if 'all_same_byte' in patterns:
            recommendations.append("All bytes are identical - check random generator")
        
        if 'sequential' in patterns:
            recommendations.append("Sequential pattern detected - avoid predictable sources")
        
        if any('known_weak' in p for p in patterns):
            recommendations.append("Known weak pattern detected - regenerate immediately")
        
        if 'low_complexity' in patterns:
            recommendations.append("Low complexity detected - increase entropy sources")
        
        if not recommendations:
            recommendations.append("Entropy quality is acceptable")
        
        return recommendations


class KeyValidator:
    """
    Validator for cryptographic keys with comprehensive security checks.
    
    This class implements validation routines to ensure generated keys
    meet cryptographic standards and security requirements.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the key validator."""
        self.config = config
        self.entropy_analyzer = EntropyAnalyzer(config)
        
    def validate_private_key(self, private_key: bytes) -> KeyValidationResult:
        """
        Validate a private key for cryptographic security.
        
        Args:
            private_key: Private key bytes to validate
            
        Returns:
            KeyValidationResult indicating validation status
        """
        if len(private_key) != 32:
            return KeyValidationResult.INVALID_FORMAT
        
        # Check if key is within valid range for secp256k1
        private_key_int = int.from_bytes(private_key, 'big')
        secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
        
        if private_key_int == 0 or private_key_int >= secp256k1_order:
            return KeyValidationResult.INVALID_FORMAT
        
        # Analyze entropy if configured
        if self.config.get('check_weak_patterns', True):
            entropy_analysis = self.entropy_analyzer.analyze_entropy(private_key)
            
            if not entropy_analysis.is_sufficient:
                if 'sequential' in entropy_analysis.patterns_detected:
                    return KeyValidationResult.SEQUENTIAL_PATTERN
                elif any('repeated' in p for p in entropy_analysis.patterns_detected):
                    return KeyValidationResult.REPEATED_PATTERN
                elif any('known_weak' in p for p in entropy_analysis.patterns_detected):
                    return KeyValidationResult.KNOWN_WEAK
                else:
                    return KeyValidationResult.WEAK_ENTROPY
        
        return KeyValidationResult.VALID
    
    def validate_checksum(self, data: bytes, checksum: bytes) -> bool:
        """
        Validate a checksum for given data.
        
        Args:
            data: Data to validate
            checksum: Expected checksum
            
        Returns:
            True if checksum is valid
        """
        calculated_checksum = hashlib.sha256(hashlib.sha256(data).digest()).digest()[:4]
        return hmac.compare_digest(calculated_checksum, checksum)
