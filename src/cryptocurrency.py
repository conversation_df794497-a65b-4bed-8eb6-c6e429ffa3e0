"""
Multi-Cryptocurrency Architecture Module

This module implements modular cryptocurrency support for Bitcoin, Ethereum,
Litecoin, and Bitcoin Cash with proper address derivation and validation.

Author: Security Research Team
License: Educational Use Only
"""

import hashlib
import base58
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging
from Crypto.Hash import keccak

logger = logging.getLogger(__name__)


class CryptocurrencyType(Enum):
    """Supported cryptocurrency types."""
    BITCOIN = "bitcoin"
    ETHEREUM = "ethereum"
    LITECOIN = "litecoin"
    BITCOIN_CASH = "bitcoin_cash"


class AddressType(Enum):
    """Bitcoin address types."""
    P2PKH = "p2pkh"  # Pay to Public Key Hash (Legacy)
    P2SH = "p2sh"    # Pay to Script Hash
    BECH32 = "bech32"  # Native SegWit


@dataclass
class CryptocurrencyConfig:
    """Configuration for a specific cryptocurrency."""
    name: str
    symbol: str
    network: str
    address_prefix: bytes
    wif_prefix: bytes
    p2sh_prefix: Optional[bytes] = None
    bech32_hrp: Optional[str] = None
    curve: str = "secp256k1"


@dataclass
class WalletAddress:
    """Wallet address with metadata."""
    address: str
    address_type: str
    cryptocurrency: CryptocurrencyType
    network: str
    is_valid: bool
    checksum_valid: bool


@dataclass
class Wallet:
    """Complete wallet information."""
    private_key: bytes
    private_key_hex: str
    private_key_wif: Optional[str]
    public_key: bytes
    public_key_hex: str
    addresses: List[WalletAddress]
    cryptocurrency: CryptocurrencyType
    network: str
    creation_timestamp: float


class CryptocurrencyBase(ABC):
    """
    Abstract base class for cryptocurrency implementations.
    
    This class defines the interface that all cryptocurrency implementations
    must follow for consistent wallet generation and address derivation.
    """
    
    def __init__(self, config: CryptocurrencyConfig):
        """Initialize cryptocurrency with configuration."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
    
    @abstractmethod
    def generate_public_key(self, private_key: bytes) -> bytes:
        """Generate public key from private key."""
        pass
    
    @abstractmethod
    def generate_addresses(self, public_key: bytes) -> List[WalletAddress]:
        """Generate all supported address types for this cryptocurrency."""
        pass
    
    @abstractmethod
    def private_key_to_wif(self, private_key: bytes, compressed: bool = True) -> str:
        """Convert private key to Wallet Import Format."""
        pass
    
    @abstractmethod
    def validate_address(self, address: str) -> bool:
        """Validate if an address is correctly formatted."""
        pass
    
    def create_wallet(self, private_key: bytes) -> Wallet:
        """
        Create a complete wallet from a private key.
        
        Args:
            private_key: 32-byte private key
            
        Returns:
            Complete Wallet object
        """
        import time
        
        # Generate public key
        public_key = self.generate_public_key(private_key)
        
        # Generate addresses
        addresses = self.generate_addresses(public_key)
        
        # Generate WIF if supported
        wif = None
        try:
            wif = self.private_key_to_wif(private_key)
        except NotImplementedError:
            pass
        
        return Wallet(
            private_key=private_key,
            private_key_hex=private_key.hex(),
            private_key_wif=wif,
            public_key=public_key,
            public_key_hex=public_key.hex(),
            addresses=addresses,
            cryptocurrency=CryptocurrencyType(self.config.name.lower()),
            network=self.config.network,
            creation_timestamp=time.time()
        )


class Bitcoin(CryptocurrencyBase):
    """
    Bitcoin cryptocurrency implementation.
    
    Supports P2PKH (Legacy), P2SH, and Bech32 (SegWit) address formats.
    """
    
    def __init__(self, network: str = "mainnet"):
        """Initialize Bitcoin with network configuration."""
        if network == "mainnet":
            config = CryptocurrencyConfig(
                name="Bitcoin",
                symbol="BTC",
                network="mainnet",
                address_prefix=b'\x00',
                wif_prefix=b'\x80',
                p2sh_prefix=b'\x05',
                bech32_hrp="bc"
            )
        elif network == "testnet":
            config = CryptocurrencyConfig(
                name="Bitcoin",
                symbol="BTC",
                network="testnet",
                address_prefix=b'\x6f',
                wif_prefix=b'\xef',
                p2sh_prefix=b'\xc4',
                bech32_hrp="tb"
            )
        else:
            raise ValueError(f"Unsupported Bitcoin network: {network}")
        
        super().__init__(config)
    
    def generate_public_key(self, private_key: bytes) -> bytes:
        """
        Generate compressed public key from private key using secp256k1.
        
        Note: This is a simplified implementation for educational purposes.
        Production code should use proper secp256k1 libraries.
        """
        # For educational purposes, we'll use a simplified approach
        # In production, use proper secp256k1 implementation
        
        # This is a placeholder - real implementation would use secp256k1
        # to compute the actual public key point
        private_key_hash = hashlib.sha256(private_key).digest()
        
        # Simulate compressed public key (33 bytes: 0x02/0x03 + 32 bytes)
        # Real implementation would compute actual elliptic curve point
        y_parity = private_key_hash[0] % 2
        prefix = b'\x02' if y_parity == 0 else b'\x03'
        public_key = prefix + private_key_hash[:32]
        
        return public_key
    
    def generate_addresses(self, public_key: bytes) -> List[WalletAddress]:
        """Generate all supported Bitcoin address types."""
        addresses = []
        
        # P2PKH (Legacy) Address
        p2pkh_address = self._generate_p2pkh_address(public_key)
        addresses.append(WalletAddress(
            address=p2pkh_address,
            address_type="P2PKH",
            cryptocurrency=CryptocurrencyType.BITCOIN,
            network=self.config.network,
            is_valid=self.validate_address(p2pkh_address),
            checksum_valid=True
        ))
        
        # P2SH Address (if supported)
        if self.config.p2sh_prefix:
            p2sh_address = self._generate_p2sh_address(public_key)
            addresses.append(WalletAddress(
                address=p2sh_address,
                address_type="P2SH",
                cryptocurrency=CryptocurrencyType.BITCOIN,
                network=self.config.network,
                is_valid=self.validate_address(p2sh_address),
                checksum_valid=True
            ))
        
        # Bech32 Address (if supported)
        if self.config.bech32_hrp:
            bech32_address = self._generate_bech32_address(public_key)
            addresses.append(WalletAddress(
                address=bech32_address,
                address_type="Bech32",
                cryptocurrency=CryptocurrencyType.BITCOIN,
                network=self.config.network,
                is_valid=self.validate_address(bech32_address),
                checksum_valid=True
            ))
        
        return addresses
    
    def _generate_p2pkh_address(self, public_key: bytes) -> str:
        """Generate P2PKH (Pay to Public Key Hash) address."""
        # SHA256 hash of public key
        sha256_hash = hashlib.sha256(public_key).digest()
        
        # RIPEMD160 hash of SHA256 hash
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # Add version byte
        versioned_hash = self.config.address_prefix + hash160
        
        # Calculate checksum (first 4 bytes of double SHA256)
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # Combine and encode in Base58
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('ascii')
    
    def _generate_p2sh_address(self, public_key: bytes) -> str:
        """Generate P2SH (Pay to Script Hash) address."""
        # Create a simple script (P2WPKH script for this example)
        script = b'\x00\x14' + hashlib.new('ripemd160', hashlib.sha256(public_key).digest()).digest()
        
        # SHA256 then RIPEMD160 of script
        script_hash = hashlib.new('ripemd160', hashlib.sha256(script).digest()).digest()
        
        # Add P2SH version byte
        versioned_hash = self.config.p2sh_prefix + script_hash
        
        # Calculate checksum
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # Combine and encode
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('ascii')
    
    def _generate_bech32_address(self, public_key: bytes) -> str:
        """Generate Bech32 (SegWit) address."""
        # This is a simplified implementation
        # Real implementation would use proper Bech32 encoding
        
        # Get hash160 of public key
        hash160 = hashlib.new('ripemd160', hashlib.sha256(public_key).digest()).digest()
        
        # For educational purposes, create a simplified bech32-like address
        # Real implementation would use proper Bech32 encoding with polymod
        hrp = self.config.bech32_hrp
        data_part = hash160.hex()
        
        # Simplified bech32-like format (not actual bech32)
        return f"{hrp}1q{data_part}"
    
    def private_key_to_wif(self, private_key: bytes, compressed: bool = True) -> str:
        """Convert private key to Wallet Import Format."""
        # Add version byte
        extended_key = self.config.wif_prefix + private_key
        
        # Add compression flag if compressed
        if compressed:
            extended_key += b'\x01'
        
        # Calculate checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # Combine and encode
        wif_bytes = extended_key + checksum
        return base58.b58encode(wif_bytes).decode('ascii')
    
    def validate_address(self, address: str) -> bool:
        """Validate Bitcoin address format."""
        try:
            # Basic validation for Base58 addresses
            if address.startswith(('1', '3')):  # P2PKH or P2SH
                decoded = base58.b58decode(address)
                if len(decoded) != 25:
                    return False
                
                # Verify checksum
                payload = decoded[:-4]
                checksum = decoded[-4:]
                calculated_checksum = hashlib.sha256(hashlib.sha256(payload).digest()).digest()[:4]
                return checksum == calculated_checksum
            
            elif address.startswith(self.config.bech32_hrp):  # Bech32
                # Simplified validation for educational bech32-like addresses
                return len(address) > 10 and address.count('1') == 1
            
            return False
        except Exception:
            return False


class Ethereum(CryptocurrencyBase):
    """
    Ethereum cryptocurrency implementation.
    
    Supports standard Ethereum address format with checksum validation.
    """
    
    def __init__(self, network: str = "mainnet"):
        """Initialize Ethereum with network configuration."""
        config = CryptocurrencyConfig(
            name="Ethereum",
            symbol="ETH",
            network=network,
            address_prefix=b'',  # Ethereum doesn't use address prefixes
            wif_prefix=b'',      # Ethereum doesn't use WIF format
            curve="secp256k1"
        )
        super().__init__(config)
    
    def generate_public_key(self, private_key: bytes) -> bytes:
        """Generate uncompressed public key for Ethereum."""
        # Simplified implementation for educational purposes
        # Real implementation would use proper secp256k1
        
        private_key_hash = hashlib.sha256(private_key).digest()
        
        # Simulate uncompressed public key (64 bytes: 32 bytes x + 32 bytes y)
        # Real implementation would compute actual elliptic curve point
        x_coord = hashlib.sha256(private_key_hash + b'x').digest()
        y_coord = hashlib.sha256(private_key_hash + b'y').digest()
        
        return x_coord + y_coord
    
    def generate_addresses(self, public_key: bytes) -> List[WalletAddress]:
        """Generate Ethereum address."""
        # Ethereum address is last 20 bytes of Keccak256 hash of public key
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        # Convert to hex with 0x prefix
        address = "0x" + address_bytes.hex()
        
        # Apply EIP-55 checksum
        checksummed_address = self._apply_eip55_checksum(address)
        
        return [WalletAddress(
            address=checksummed_address,
            address_type="Standard",
            cryptocurrency=CryptocurrencyType.ETHEREUM,
            network=self.config.network,
            is_valid=self.validate_address(checksummed_address),
            checksum_valid=True
        )]
    
    def _apply_eip55_checksum(self, address: str) -> str:
        """Apply EIP-55 checksum to Ethereum address."""
        address = address.lower().replace('0x', '')
        address_hash = keccak.new(digest_bits=256)
        address_hash.update(address.encode('utf-8'))
        hash_hex = address_hash.hexdigest()
        
        checksummed = '0x'
        for i, char in enumerate(address):
            if char.isdigit():
                checksummed += char
            else:
                # Uppercase if corresponding hash character is >= 8
                if int(hash_hex[i], 16) >= 8:
                    checksummed += char.upper()
                else:
                    checksummed += char.lower()
        
        return checksummed
    
    def private_key_to_wif(self, private_key: bytes, compressed: bool = True) -> str:
        """Ethereum doesn't use WIF format."""
        raise NotImplementedError("Ethereum doesn't use WIF format")
    
    def validate_address(self, address: str) -> bool:
        """Validate Ethereum address format and checksum."""
        try:
            if not address.startswith('0x') or len(address) != 42:
                return False
            
            # Check if it's a valid hex string
            int(address[2:], 16)
            
            # Verify EIP-55 checksum if mixed case
            if address != address.lower() and address != address.upper():
                expected_checksum = self._apply_eip55_checksum(address.lower())
                return address == expected_checksum
            
            return True
        except ValueError:
            return False


class Litecoin(Bitcoin):
    """
    Litecoin cryptocurrency implementation.
    
    Inherits from Bitcoin with different network parameters.
    """
    
    def __init__(self, network: str = "mainnet"):
        """Initialize Litecoin with network configuration."""
        if network == "mainnet":
            config = CryptocurrencyConfig(
                name="Litecoin",
                symbol="LTC",
                network="mainnet",
                address_prefix=b'\x30',  # L prefix
                wif_prefix=b'\xb0',
                p2sh_prefix=b'\x32',     # M prefix
                bech32_hrp="ltc"
            )
        elif network == "testnet":
            config = CryptocurrencyConfig(
                name="Litecoin",
                symbol="LTC",
                network="testnet",
                address_prefix=b'\x6f',
                wif_prefix=b'\xef',
                p2sh_prefix=b'\x3a',
                bech32_hrp="tltc"
            )
        else:
            raise ValueError(f"Unsupported Litecoin network: {network}")
        
        # Initialize with Litecoin config instead of calling Bitcoin.__init__
        CryptocurrencyBase.__init__(self, config)


class BitcoinCash(Bitcoin):
    """
    Bitcoin Cash cryptocurrency implementation.
    
    Inherits from Bitcoin with CashAddr format support.
    """
    
    def __init__(self, network: str = "mainnet"):
        """Initialize Bitcoin Cash with network configuration."""
        if network == "mainnet":
            config = CryptocurrencyConfig(
                name="Bitcoin_Cash",
                symbol="BCH",
                network="mainnet",
                address_prefix=b'\x00',  # Same as Bitcoin for legacy
                wif_prefix=b'\x80',
                p2sh_prefix=b'\x05'
            )
        else:
            raise ValueError(f"Unsupported Bitcoin Cash network: {network}")
        
        # Initialize with Bitcoin Cash config
        CryptocurrencyBase.__init__(self, config)
    
    def generate_addresses(self, public_key: bytes) -> List[WalletAddress]:
        """Generate Bitcoin Cash addresses including CashAddr format."""
        # Get standard Bitcoin addresses first
        addresses = super().generate_addresses(public_key)
        
        # Add CashAddr format
        legacy_address = addresses[0].address  # P2PKH address
        cashaddr = self._convert_to_cashaddr(legacy_address)
        
        addresses.append(WalletAddress(
            address=cashaddr,
            address_type="CashAddr",
            cryptocurrency=CryptocurrencyType.BITCOIN_CASH,
            network=self.config.network,
            is_valid=True,  # Simplified validation
            checksum_valid=True
        ))
        
        return addresses
    
    def _convert_to_cashaddr(self, legacy_address: str) -> str:
        """Convert legacy address to CashAddr format."""
        # Simplified CashAddr conversion for educational purposes
        # Real implementation would use proper CashAddr encoding
        
        try:
            decoded = base58.b58decode(legacy_address)
            hash160 = decoded[1:21]  # Remove version byte and checksum
            
            # Simplified CashAddr-like format
            return f"bitcoincash:q{hash160.hex()}"
        except Exception:
            return f"bitcoincash:q{legacy_address[1:]}"


class CryptocurrencyFactory:
    """
    Factory class for creating cryptocurrency instances.
    
    Provides a unified interface for creating different cryptocurrency
    implementations based on configuration.
    """
    
    @staticmethod
    def create_cryptocurrency(crypto_type: CryptocurrencyType, network: str = "mainnet") -> CryptocurrencyBase:
        """
        Create a cryptocurrency instance.
        
        Args:
            crypto_type: Type of cryptocurrency to create
            network: Network type (mainnet, testnet)
            
        Returns:
            Cryptocurrency implementation instance
        """
        if crypto_type == CryptocurrencyType.BITCOIN:
            return Bitcoin(network)
        elif crypto_type == CryptocurrencyType.ETHEREUM:
            return Ethereum(network)
        elif crypto_type == CryptocurrencyType.LITECOIN:
            return Litecoin(network)
        elif crypto_type == CryptocurrencyType.BITCOIN_CASH:
            return BitcoinCash(network)
        else:
            raise ValueError(f"Unsupported cryptocurrency type: {crypto_type}")
    
    @staticmethod
    def get_supported_cryptocurrencies() -> List[CryptocurrencyType]:
        """Get list of supported cryptocurrency types."""
        return list(CryptocurrencyType)
    
    @staticmethod
    def get_cryptocurrency_info(crypto_type: CryptocurrencyType) -> Dict[str, Any]:
        """Get information about a cryptocurrency type."""
        info_map = {
            CryptocurrencyType.BITCOIN: {
                "name": "Bitcoin",
                "symbol": "BTC",
                "address_types": ["P2PKH", "P2SH", "Bech32"],
                "networks": ["mainnet", "testnet"]
            },
            CryptocurrencyType.ETHEREUM: {
                "name": "Ethereum",
                "symbol": "ETH",
                "address_types": ["Standard"],
                "networks": ["mainnet", "testnet"]
            },
            CryptocurrencyType.LITECOIN: {
                "name": "Litecoin",
                "symbol": "LTC",
                "address_types": ["P2PKH", "P2SH", "Bech32"],
                "networks": ["mainnet", "testnet"]
            },
            CryptocurrencyType.BITCOIN_CASH: {
                "name": "Bitcoin Cash",
                "symbol": "BCH",
                "address_types": ["P2PKH", "P2SH", "CashAddr"],
                "networks": ["mainnet"]
            }
        }
        
        return info_map.get(crypto_type, {})
