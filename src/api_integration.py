"""
API Integration and Balance Detection System

This module implements multi-provider API integration for reliable balance
checking with rate limiting, caching, and comprehensive error handling.

Author: Security Research Team
License: Educational Use Only
"""

import asyncio
import aiohttp
import time
import json
import hashlib
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class APIProvider(Enum):
    """Supported API providers."""
    BLOCKCYPHER = "blockcypher"
    BLOCKSTREAM = "blockstream"
    BLOCKCHAIN_INFO = "blockchain_info"
    ETHERSCAN = "etherscan"
    INFURA = "infura"
    ALCHEMY = "alchemy"


class TransactionType(Enum):
    """Transaction types."""
    INCOMING = "incoming"
    OUTGOING = "outgoing"
    INTERNAL = "internal"


@dataclass
class Transaction:
    """Transaction information."""
    txid: str
    amount: float
    transaction_type: TransactionType
    confirmations: int
    timestamp: datetime
    block_height: Optional[int] = None
    fee: Optional[float] = None
    from_address: Optional[str] = None
    to_address: Optional[str] = None


@dataclass
class BalanceInfo:
    """Comprehensive balance information."""
    address: str
    cryptocurrency: str
    balance: float
    unconfirmed_balance: float
    total_received: float
    total_sent: float
    transaction_count: int
    transactions: List[Transaction]
    last_updated: datetime
    api_source: str
    has_activity: bool


@dataclass
class APIResponse:
    """Standardized API response."""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    provider: Optional[APIProvider] = None
    response_time_ms: Optional[float] = None
    cached: bool = False


class RateLimiter:
    """
    Advanced rate limiter with exponential backoff and burst handling.
    
    Implements token bucket algorithm with configurable parameters
    for different API providers.
    """
    
    def __init__(self, requests_per_second: float, burst_limit: int = 5):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_second: Maximum requests per second
            burst_limit: Maximum burst requests allowed
        """
        self.requests_per_second = requests_per_second
        self.burst_limit = burst_limit
        self.tokens = burst_limit
        self.last_update = time.time()
        self.request_times = []
        
    async def acquire(self) -> None:
        """Acquire permission to make a request."""
        current_time = time.time()
        
        # Add tokens based on elapsed time
        elapsed = current_time - self.last_update
        self.tokens = min(
            self.burst_limit,
            self.tokens + elapsed * self.requests_per_second
        )
        self.last_update = current_time
        
        # Wait if no tokens available
        if self.tokens < 1:
            wait_time = (1 - self.tokens) / self.requests_per_second
            logger.debug(f"Rate limiting: waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)
            self.tokens = 0
        else:
            self.tokens -= 1
        
        # Track request times for statistics
        self.request_times.append(current_time)
        # Keep only last 100 requests
        if len(self.request_times) > 100:
            self.request_times = self.request_times[-100:]
    
    def get_current_rate(self) -> float:
        """Get current request rate."""
        if len(self.request_times) < 2:
            return 0.0
        
        current_time = time.time()
        recent_requests = [t for t in self.request_times if current_time - t <= 60]
        
        if len(recent_requests) < 2:
            return 0.0
        
        time_span = recent_requests[-1] - recent_requests[0]
        return len(recent_requests) / max(time_span, 1.0)


class ResponseCache:
    """
    Intelligent caching system for API responses.
    
    Implements TTL-based caching with automatic cleanup and
    cache hit/miss statistics.
    """
    
    def __init__(self, ttl_seconds: int = 300, max_entries: int = 10000):
        """
        Initialize response cache.
        
        Args:
            ttl_seconds: Time to live for cached entries
            max_entries: Maximum number of cached entries
        """
        self.ttl_seconds = ttl_seconds
        self.max_entries = max_entries
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.hits = 0
        self.misses = 0
        
    def _generate_key(self, provider: APIProvider, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate cache key for request."""
        key_data = f"{provider.value}:{endpoint}:{json.dumps(params, sort_keys=True)}"
        return hashlib.sha256(key_data.encode()).hexdigest()
    
    def get(self, provider: APIProvider, endpoint: str, params: Dict[str, Any]) -> Optional[Any]:
        """Get cached response if available and not expired."""
        key = self._generate_key(provider, endpoint, params)
        
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp <= self.ttl_seconds:
                self.hits += 1
                logger.debug(f"Cache hit for {provider.value}:{endpoint}")
                return data
            else:
                # Remove expired entry
                del self.cache[key]
        
        self.misses += 1
        return None
    
    def set(self, provider: APIProvider, endpoint: str, params: Dict[str, Any], data: Any) -> None:
        """Cache response data."""
        key = self._generate_key(provider, endpoint, params)
        
        # Remove oldest entries if cache is full
        if len(self.cache) >= self.max_entries:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        self.cache[key] = (data, time.time())
        logger.debug(f"Cached response for {provider.value}:{endpoint}")
    
    def clear_expired(self) -> int:
        """Clear expired cache entries and return count removed."""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp > self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.debug(f"Cleared {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0.0
        
        return {
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": hit_rate,
            "cached_entries": len(self.cache)
        }


class APIProviderBase(ABC):
    """
    Abstract base class for API providers.
    
    Defines the interface for cryptocurrency API providers with
    standardized methods for balance checking and transaction history.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize API provider with configuration."""
        self.config = config
        self.rate_limiter = RateLimiter(
            config.get('requests_per_second', 1.0),
            config.get('burst_limit', 5)
        )
        self.base_url = config.get('base_url', '')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)
        
    @abstractmethod
    async def get_balance(self, address: str, cryptocurrency: str) -> APIResponse:
        """Get balance information for an address."""
        pass
    
    @abstractmethod
    async def get_transactions(self, address: str, cryptocurrency: str, limit: int = 50) -> APIResponse:
        """Get transaction history for an address."""
        pass
    
    async def _make_request(self, url: str, params: Optional[Dict[str, Any]] = None) -> APIResponse:
        """Make HTTP request with rate limiting and error handling."""
        await self.rate_limiter.acquire()
        
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        data = await response.json()
                        return APIResponse(
                            success=True,
                            data=data,
                            response_time_ms=response_time
                        )
                    else:
                        error_text = await response.text()
                        return APIResponse(
                            success=False,
                            error=f"HTTP {response.status}: {error_text}",
                            response_time_ms=response_time
                        )
        
        except asyncio.TimeoutError:
            return APIResponse(
                success=False,
                error="Request timeout",
                response_time_ms=(time.time() - start_time) * 1000
            )
        except Exception as e:
            return APIResponse(
                success=False,
                error=str(e),
                response_time_ms=(time.time() - start_time) * 1000
            )


class BlockCypherAPI(APIProviderBase):
    """BlockCypher API implementation for Bitcoin, Litecoin, and Bitcoin Cash."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize BlockCypher API."""
        config.setdefault('base_url', 'https://api.blockcypher.com/v1')
        super().__init__(config)
        
        self.network_map = {
            'bitcoin': 'btc/main',
            'litecoin': 'ltc/main',
            'bitcoin_cash': 'bch/main'
        }
    
    async def get_balance(self, address: str, cryptocurrency: str) -> APIResponse:
        """Get balance from BlockCypher API."""
        if cryptocurrency not in self.network_map:
            return APIResponse(
                success=False,
                error=f"Unsupported cryptocurrency: {cryptocurrency}",
                provider=APIProvider.BLOCKCYPHER
            )
        
        network = self.network_map[cryptocurrency]
        url = f"{self.base_url}/{network}/addrs/{address}/balance"
        
        params = {}
        if self.api_key:
            params['token'] = self.api_key
        
        response = await self._make_request(url, params)
        response.provider = APIProvider.BLOCKCYPHER
        
        if response.success and response.data:
            # Convert satoshis to main unit
            balance = response.data.get('balance', 0) / 100000000
            unconfirmed = response.data.get('unconfirmed_balance', 0) / 100000000
            total_received = response.data.get('total_received', 0) / 100000000
            total_sent = response.data.get('total_sent', 0) / 100000000
            
            balance_info = BalanceInfo(
                address=address,
                cryptocurrency=cryptocurrency,
                balance=balance,
                unconfirmed_balance=unconfirmed,
                total_received=total_received,
                total_sent=total_sent,
                transaction_count=response.data.get('n_tx', 0),
                transactions=[],  # Will be populated by get_transactions
                last_updated=datetime.now(),
                api_source="blockcypher",
                has_activity=total_received > 0 or total_sent > 0
            )
            
            response.data = balance_info
        
        return response
    
    async def get_transactions(self, address: str, cryptocurrency: str, limit: int = 50) -> APIResponse:
        """Get transaction history from BlockCypher API."""
        if cryptocurrency not in self.network_map:
            return APIResponse(
                success=False,
                error=f"Unsupported cryptocurrency: {cryptocurrency}",
                provider=APIProvider.BLOCKCYPHER
            )
        
        network = self.network_map[cryptocurrency]
        url = f"{self.base_url}/{network}/addrs/{address}"
        
        params = {'limit': min(limit, 200)}  # BlockCypher limit
        if self.api_key:
            params['token'] = self.api_key
        
        response = await self._make_request(url, params)
        response.provider = APIProvider.BLOCKCYPHER
        
        if response.success and response.data:
            transactions = []
            
            for tx_ref in response.data.get('txrefs', []):
                # Determine transaction type
                if tx_ref.get('tx_output_n', -1) >= 0:
                    tx_type = TransactionType.INCOMING
                else:
                    tx_type = TransactionType.OUTGOING
                
                # Convert timestamp
                timestamp = datetime.fromisoformat(
                    tx_ref.get('confirmed', '').replace('Z', '+00:00')
                ) if tx_ref.get('confirmed') else datetime.now()
                
                transaction = Transaction(
                    txid=tx_ref.get('tx_hash', ''),
                    amount=tx_ref.get('value', 0) / 100000000,
                    transaction_type=tx_type,
                    confirmations=tx_ref.get('confirmations', 0),
                    timestamp=timestamp,
                    block_height=tx_ref.get('block_height')
                )
                
                transactions.append(transaction)
            
            response.data = transactions
        
        return response


class EtherscanAPI(APIProviderBase):
    """Etherscan API implementation for Ethereum."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Etherscan API."""
        config.setdefault('base_url', 'https://api.etherscan.io/api')
        super().__init__(config)
    
    async def get_balance(self, address: str, cryptocurrency: str) -> APIResponse:
        """Get balance from Etherscan API."""
        if cryptocurrency != 'ethereum':
            return APIResponse(
                success=False,
                error=f"Unsupported cryptocurrency: {cryptocurrency}",
                provider=APIProvider.ETHERSCAN
            )
        
        params = {
            'module': 'account',
            'action': 'balance',
            'address': address,
            'tag': 'latest'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        response = await self._make_request(self.base_url, params)
        response.provider = APIProvider.ETHERSCAN
        
        if response.success and response.data:
            if response.data.get('status') == '1':
                # Convert wei to ether
                balance_wei = int(response.data.get('result', '0'))
                balance_eth = balance_wei / 10**18
                
                balance_info = BalanceInfo(
                    address=address,
                    cryptocurrency=cryptocurrency,
                    balance=balance_eth,
                    unconfirmed_balance=0.0,  # Ethereum doesn't have unconfirmed balance concept
                    total_received=0.0,  # Would need additional API calls
                    total_sent=0.0,      # Would need additional API calls
                    transaction_count=0,  # Would need additional API calls
                    transactions=[],
                    last_updated=datetime.now(),
                    api_source="etherscan",
                    has_activity=balance_eth > 0
                )
                
                response.data = balance_info
            else:
                response.success = False
                response.error = response.data.get('message', 'Unknown error')
        
        return response
    
    async def get_transactions(self, address: str, cryptocurrency: str, limit: int = 50) -> APIResponse:
        """Get transaction history from Etherscan API."""
        if cryptocurrency != 'ethereum':
            return APIResponse(
                success=False,
                error=f"Unsupported cryptocurrency: {cryptocurrency}",
                provider=APIProvider.ETHERSCAN
            )
        
        params = {
            'module': 'account',
            'action': 'txlist',
            'address': address,
            'startblock': 0,
            'endblock': ********,
            'page': 1,
            'offset': min(limit, 10000),  # Etherscan limit
            'sort': 'desc'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        response = await self._make_request(self.base_url, params)
        response.provider = APIProvider.ETHERSCAN
        
        if response.success and response.data:
            if response.data.get('status') == '1':
                transactions = []
                
                for tx in response.data.get('result', []):
                    # Determine transaction type
                    if tx.get('to', '').lower() == address.lower():
                        tx_type = TransactionType.INCOMING
                    else:
                        tx_type = TransactionType.OUTGOING
                    
                    # Convert timestamp
                    timestamp = datetime.fromtimestamp(int(tx.get('timeStamp', 0)))
                    
                    # Convert wei to ether
                    amount_wei = int(tx.get('value', '0'))
                    amount_eth = amount_wei / 10**18
                    
                    transaction = Transaction(
                        txid=tx.get('hash', ''),
                        amount=amount_eth,
                        transaction_type=tx_type,
                        confirmations=0,  # Would need current block number to calculate
                        timestamp=timestamp,
                        block_height=int(tx.get('blockNumber', 0)),
                        fee=(int(tx.get('gasUsed', 0)) * int(tx.get('gasPrice', 0))) / 10**18,
                        from_address=tx.get('from'),
                        to_address=tx.get('to')
                    )
                    
                    transactions.append(transaction)
                
                response.data = transactions
            else:
                response.success = False
                response.error = response.data.get('message', 'Unknown error')
        
        return response


class APIManager:
    """
    Comprehensive API manager with multi-provider support and failover.
    
    Manages multiple API providers with automatic failover, load balancing,
    and intelligent caching for optimal performance and reliability.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize API manager with configuration."""
        self.config = config
        self.cache = ResponseCache(
            ttl_seconds=config.get('cache_ttl_seconds', 300),
            max_entries=config.get('cache_max_entries', 10000)
        )
        
        # Initialize API providers
        self.providers: Dict[APIProvider, APIProviderBase] = {}
        self._initialize_providers()
        
        # Statistics
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        
        logger.info(f"APIManager initialized with {len(self.providers)} providers")
    
    def _initialize_providers(self) -> None:
        """Initialize configured API providers."""
        provider_configs = self.config.get('providers', {})
        
        # Initialize BlockCypher
        if 'blockcypher' in provider_configs:
            self.providers[APIProvider.BLOCKCYPHER] = BlockCypherAPI(
                provider_configs['blockcypher']
            )
        
        # Initialize Etherscan
        if 'etherscan' in provider_configs:
            self.providers[APIProvider.ETHERSCAN] = EtherscanAPI(
                provider_configs['etherscan']
            )
        
        # Add more providers as needed
    
    async def get_balance_with_fallback(self, address: str, cryptocurrency: str) -> BalanceInfo:
        """
        Get balance with automatic provider fallback.
        
        Args:
            address: Cryptocurrency address
            cryptocurrency: Cryptocurrency type
            
        Returns:
            BalanceInfo object with comprehensive balance data
        """
        # Check cache first
        cached_result = self.cache.get(
            APIProvider.BLOCKCYPHER,  # Use primary provider for cache key
            'balance',
            {'address': address, 'cryptocurrency': cryptocurrency}
        )
        
        if cached_result:
            self.cache_hits += 1
            logger.debug(f"Returning cached balance for {address}")
            return cached_result
        
        # Try providers in order of preference
        provider_order = self._get_provider_order(cryptocurrency)
        
        for provider in provider_order:
            if provider not in self.providers:
                continue
            
            try:
                self.request_count += 1
                response = await self.providers[provider].get_balance(address, cryptocurrency)
                
                if response.success and response.data:
                    # Cache successful response
                    self.cache.set(
                        provider,
                        'balance',
                        {'address': address, 'cryptocurrency': cryptocurrency},
                        response.data
                    )
                    
                    logger.debug(f"Successfully got balance from {provider.value}")
                    return response.data
                else:
                    logger.warning(f"Failed to get balance from {provider.value}: {response.error}")
                    self.error_count += 1
            
            except Exception as e:
                logger.error(f"Exception with provider {provider.value}: {e}")
                self.error_count += 1
                continue
        
        # Return empty balance if all providers failed
        logger.error(f"All providers failed for address {address}")
        return BalanceInfo(
            address=address,
            cryptocurrency=cryptocurrency,
            balance=0.0,
            unconfirmed_balance=0.0,
            total_received=0.0,
            total_sent=0.0,
            transaction_count=0,
            transactions=[],
            last_updated=datetime.now(),
            api_source="none",
            has_activity=False
        )
    
    def _get_provider_order(self, cryptocurrency: str) -> List[APIProvider]:
        """Get preferred provider order for a cryptocurrency."""
        if cryptocurrency == 'ethereum':
            return [APIProvider.ETHERSCAN, APIProvider.INFURA, APIProvider.ALCHEMY]
        else:
            return [APIProvider.BLOCKCYPHER, APIProvider.BLOCKSTREAM, APIProvider.BLOCKCHAIN_INFO]
    
    async def cleanup_cache(self) -> None:
        """Clean up expired cache entries."""
        removed_count = self.cache.clear_expired()
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} expired cache entries")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get API manager statistics."""
        cache_stats = self.cache.get_stats()
        
        return {
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "cache_stats": cache_stats,
            "active_providers": len(self.providers)
        }
