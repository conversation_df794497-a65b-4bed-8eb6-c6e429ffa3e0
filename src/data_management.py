"""
Professional Data Management System

This module implements structured JSON output with schema validation,
comprehensive metadata handling, secure file operations, and data integrity verification.

Author: Security Research Team
License: Educational Use Only
"""

import json
import os
import shutil
import gzip
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import logging
from jsonschema import validate, ValidationError
import stat

logger = logging.getLogger(__name__)


@dataclass
class WalletMetadata:
    """Comprehensive wallet metadata."""
    generation_timestamp: float
    generation_method: str
    entropy_source: str
    validation_status: str
    cryptocurrency: str
    network: str
    address_types: List[str]
    api_sources_checked: List[str]
    balance_check_timestamp: Optional[float] = None
    last_activity_check: Optional[float] = None
    security_warnings: List[str] = None
    research_notes: Optional[str] = None


@dataclass
class WalletRecord:
    """Complete wallet record with all data and metadata."""
    wallet_id: str
    private_key_hex: str
    public_key_hex: str
    addresses: Dict[str, str]  # address_type -> address
    balance_info: Dict[str, Any]
    transaction_history: List[Dict[str, Any]]
    metadata: WalletMetadata
    checksum: str
    schema_version: str = "2.0.0"


@dataclass
class DataExportSummary:
    """Summary of data export operation."""
    export_timestamp: float
    total_wallets: int
    file_path: str
    file_size_bytes: int
    compression_ratio: Optional[float]
    checksum: str
    schema_version: str
    export_format: str


class SchemaValidator:
    """
    JSON schema validator for wallet data structures.
    
    Ensures data integrity and consistency across all exported data.
    """
    
    def __init__(self):
        """Initialize schema validator with predefined schemas."""
        self.schemas = self._load_schemas()
        logger.info("Schema validator initialized")
    
    def _load_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Load JSON schemas for validation."""
        return {
            "wallet_record": {
                "type": "object",
                "required": [
                    "wallet_id", "private_key_hex", "public_key_hex", 
                    "addresses", "balance_info", "metadata", "checksum", "schema_version"
                ],
                "properties": {
                    "wallet_id": {"type": "string", "minLength": 1},
                    "private_key_hex": {"type": "string", "pattern": "^[0-9a-fA-F]{64}$"},
                    "public_key_hex": {"type": "string", "minLength": 66},
                    "addresses": {
                        "type": "object",
                        "additionalProperties": {"type": "string"}
                    },
                    "balance_info": {"type": "object"},
                    "transaction_history": {"type": "array"},
                    "metadata": {
                        "type": "object",
                        "required": [
                            "generation_timestamp", "generation_method", 
                            "entropy_source", "validation_status", 
                            "cryptocurrency", "network"
                        ]
                    },
                    "checksum": {"type": "string", "minLength": 64},
                    "schema_version": {"type": "string"}
                }
            },
            "export_summary": {
                "type": "object",
                "required": [
                    "export_timestamp", "total_wallets", "file_path", 
                    "file_size_bytes", "checksum", "schema_version", "export_format"
                ],
                "properties": {
                    "export_timestamp": {"type": "number"},
                    "total_wallets": {"type": "integer", "minimum": 0},
                    "file_path": {"type": "string"},
                    "file_size_bytes": {"type": "integer", "minimum": 0},
                    "compression_ratio": {"type": ["number", "null"]},
                    "checksum": {"type": "string"},
                    "schema_version": {"type": "string"},
                    "export_format": {"type": "string"}
                }
            }
        }
    
    def validate_wallet_record(self, wallet_record: Dict[str, Any]) -> bool:
        """
        Validate a wallet record against the schema.
        
        Args:
            wallet_record: Wallet record to validate
            
        Returns:
            True if valid, raises ValidationError if invalid
        """
        try:
            validate(instance=wallet_record, schema=self.schemas["wallet_record"])
            return True
        except ValidationError as e:
            logger.error(f"Wallet record validation failed: {e.message}")
            raise
    
    def validate_export_summary(self, export_summary: Dict[str, Any]) -> bool:
        """
        Validate an export summary against the schema.
        
        Args:
            export_summary: Export summary to validate
            
        Returns:
            True if valid, raises ValidationError if invalid
        """
        try:
            validate(instance=export_summary, schema=self.schemas["export_summary"])
            return True
        except ValidationError as e:
            logger.error(f"Export summary validation failed: {e.message}")
            raise


class DataIntegrityManager:
    """
    Data integrity verification and checksum management.
    
    Ensures data hasn't been corrupted or tampered with through
    cryptographic checksums and integrity verification.
    """
    
    def __init__(self):
        """Initialize data integrity manager."""
        self.hash_algorithm = 'sha256'
        logger.info("Data integrity manager initialized")
    
    def calculate_wallet_checksum(self, wallet_data: Dict[str, Any]) -> str:
        """
        Calculate checksum for wallet data.
        
        Args:
            wallet_data: Wallet data dictionary (excluding checksum field)
            
        Returns:
            Hexadecimal checksum string
        """
        # Create a copy without the checksum field
        data_copy = wallet_data.copy()
        data_copy.pop('checksum', None)
        
        # Convert to canonical JSON string
        json_string = json.dumps(data_copy, sort_keys=True, separators=(',', ':'))
        
        # Calculate hash
        hash_obj = hashlib.new(self.hash_algorithm)
        hash_obj.update(json_string.encode('utf-8'))
        
        return hash_obj.hexdigest()
    
    def verify_wallet_checksum(self, wallet_data: Dict[str, Any]) -> bool:
        """
        Verify wallet data checksum.
        
        Args:
            wallet_data: Wallet data dictionary with checksum
            
        Returns:
            True if checksum is valid
        """
        stored_checksum = wallet_data.get('checksum')
        if not stored_checksum:
            logger.warning("No checksum found in wallet data")
            return False
        
        calculated_checksum = self.calculate_wallet_checksum(wallet_data)
        
        is_valid = stored_checksum == calculated_checksum
        if not is_valid:
            logger.error(f"Checksum mismatch: stored={stored_checksum}, calculated={calculated_checksum}")
        
        return is_valid
    
    def calculate_file_checksum(self, file_path: Union[str, Path]) -> str:
        """
        Calculate checksum for a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Hexadecimal checksum string
        """
        hash_obj = hashlib.new(self.hash_algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    def verify_file_integrity(self, file_path: Union[str, Path], expected_checksum: str) -> bool:
        """
        Verify file integrity using checksum.
        
        Args:
            file_path: Path to the file
            expected_checksum: Expected checksum
            
        Returns:
            True if file integrity is verified
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        calculated_checksum = self.calculate_file_checksum(file_path)
        
        is_valid = calculated_checksum == expected_checksum
        if not is_valid:
            logger.error(f"File integrity check failed for {file_path}")
        
        return is_valid


class SecureFileManager:
    """
    Secure file handling with proper permissions and safe operations.
    
    Implements secure file operations with appropriate permissions,
    atomic writes, and secure deletion capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize secure file manager."""
        self.config = config
        self.base_directory = Path(config.get('base_directory', './output'))
        self.file_permissions = config.get('file_permissions', '600')
        self.secure_deletion = config.get('secure_deletion', True)
        self.backup_enabled = config.get('backup_enabled', True)
        
        # Create base directory if it doesn't exist
        self.base_directory.mkdir(parents=True, exist_ok=True)
        
        # Set directory permissions
        os.chmod(self.base_directory, 0o700)  # Owner read/write/execute only
        
        logger.info(f"Secure file manager initialized with base directory: {self.base_directory}")
    
    def write_file_securely(self, file_path: Union[str, Path], data: Union[str, bytes], 
                           atomic: bool = True) -> bool:
        """
        Write file with secure permissions and atomic operation.
        
        Args:
            file_path: Path to write the file
            data: Data to write
            atomic: Whether to use atomic write operation
            
        Returns:
            True if successful
        """
        file_path = Path(file_path)
        
        # Ensure the file is within the base directory
        if not self._is_safe_path(file_path):
            logger.error(f"Unsafe file path: {file_path}")
            return False
        
        # Create parent directories if needed
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if atomic:
                # Atomic write using temporary file
                temp_path = file_path.with_suffix(file_path.suffix + '.tmp')
                
                # Write to temporary file
                mode = 'wb' if isinstance(data, bytes) else 'w'
                encoding = None if isinstance(data, bytes) else 'utf-8'
                
                with open(temp_path, mode, encoding=encoding) as f:
                    f.write(data)
                    f.flush()
                    os.fsync(f.fileno())  # Force write to disk
                
                # Set secure permissions on temporary file
                os.chmod(temp_path, int(self.file_permissions, 8))
                
                # Atomic move
                shutil.move(str(temp_path), str(file_path))
            else:
                # Direct write
                mode = 'wb' if isinstance(data, bytes) else 'w'
                encoding = None if isinstance(data, bytes) else 'utf-8'
                
                with open(file_path, mode, encoding=encoding) as f:
                    f.write(data)
                    f.flush()
                    os.fsync(f.fileno())
                
                # Set secure permissions
                os.chmod(file_path, int(self.file_permissions, 8))
            
            logger.debug(f"File written securely: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write file {file_path}: {e}")
            return False
    
    def read_file_securely(self, file_path: Union[str, Path]) -> Optional[Union[str, bytes]]:
        """
        Read file with security checks.
        
        Args:
            file_path: Path to read the file
            
        Returns:
            File contents or None if failed
        """
        file_path = Path(file_path)
        
        # Security checks
        if not self._is_safe_path(file_path):
            logger.error(f"Unsafe file path: {file_path}")
            return None
        
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        # Check file permissions
        file_stat = file_path.stat()
        if file_stat.st_mode & stat.S_IROTH or file_stat.st_mode & stat.S_IRGRP:
            logger.warning(f"File has overly permissive permissions: {file_path}")
        
        try:
            # Try to read as text first, fall back to binary
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except UnicodeDecodeError:
                with open(file_path, 'rb') as f:
                    return f.read()
                    
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            return None
    
    def delete_file_securely(self, file_path: Union[str, Path]) -> bool:
        """
        Securely delete a file.
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            True if successful
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return True  # Already deleted
        
        if not self._is_safe_path(file_path):
            logger.error(f"Unsafe file path: {file_path}")
            return False
        
        try:
            if self.secure_deletion:
                # Overwrite file with random data before deletion
                file_size = file_path.stat().st_size
                
                with open(file_path, 'r+b') as f:
                    # Overwrite with random data (3 passes)
                    for _ in range(3):
                        f.seek(0)
                        f.write(os.urandom(file_size))
                        f.flush()
                        os.fsync(f.fileno())
            
            # Delete the file
            file_path.unlink()
            logger.debug(f"File deleted securely: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def _is_safe_path(self, file_path: Path) -> bool:
        """
        Check if file path is safe (within base directory).
        
        Args:
            file_path: Path to check
            
        Returns:
            True if path is safe
        """
        try:
            # Resolve absolute paths
            abs_file_path = file_path.resolve()
            abs_base_path = self.base_directory.resolve()
            
            # Check if file path is within base directory
            return abs_base_path in abs_file_path.parents or abs_base_path == abs_file_path
            
        except Exception:
            return False
    
    def create_backup(self, file_path: Union[str, Path]) -> Optional[Path]:
        """
        Create a backup of a file.
        
        Args:
            file_path: Path to the file to backup
            
        Returns:
            Path to backup file or None if failed
        """
        if not self.backup_enabled:
            return None
        
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.error(f"Cannot backup non-existent file: {file_path}")
            return None
        
        # Create backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = file_path.with_suffix(f".{timestamp}.backup")
        
        try:
            shutil.copy2(file_path, backup_path)
            os.chmod(backup_path, int(self.file_permissions, 8))
            
            logger.debug(f"Backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return None


class DataExporter:
    """
    Comprehensive data export system with multiple formats and compression.
    
    Handles exporting wallet data in various formats with proper metadata,
    compression, and integrity verification.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize data exporter."""
        self.config = config
        self.file_manager = SecureFileManager(config)
        self.integrity_manager = DataIntegrityManager()
        self.schema_validator = SchemaValidator()
        self.compress_large_files = config.get('compress_large_files', True)
        self.compression_threshold_mb = config.get('compression_threshold_mb', 10)
        
        logger.info("Data exporter initialized")
    
    def export_wallets(self, wallets: List[WalletRecord], 
                      export_format: str = "json",
                      compress: Optional[bool] = None) -> Optional[DataExportSummary]:
        """
        Export wallet records to file.
        
        Args:
            wallets: List of wallet records to export
            export_format: Export format ("json", "jsonl")
            compress: Whether to compress the output (auto-detect if None)
            
        Returns:
            DataExportSummary or None if failed
        """
        if not wallets:
            logger.warning("No wallets to export")
            return None
        
        # Generate export filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"wallet_export_{timestamp}.{export_format}"
        
        if compress is None:
            # Auto-detect compression need based on estimated size
            estimated_size_mb = len(wallets) * 2  # Rough estimate: 2KB per wallet
            compress = self.compress_large_files and estimated_size_mb > self.compression_threshold_mb
        
        if compress:
            filename += ".gz"
        
        file_path = self.file_manager.base_directory / filename
        
        try:
            # Validate all wallet records
            for i, wallet in enumerate(wallets):
                wallet_dict = asdict(wallet)
                if not self.schema_validator.validate_wallet_record(wallet_dict):
                    logger.error(f"Wallet {i} failed validation")
                    return None
            
            # Export data
            if export_format == "json":
                export_data = [asdict(wallet) for wallet in wallets]
                json_data = json.dumps(export_data, indent=2, default=str)
            elif export_format == "jsonl":
                json_lines = [json.dumps(asdict(wallet), default=str) for wallet in wallets]
                json_data = '\n'.join(json_lines)
            else:
                logger.error(f"Unsupported export format: {export_format}")
                return None
            
            # Write file
            if compress:
                compressed_data = gzip.compress(json_data.encode('utf-8'))
                success = self.file_manager.write_file_securely(file_path, compressed_data)
            else:
                success = self.file_manager.write_file_securely(file_path, json_data)
            
            if not success:
                logger.error("Failed to write export file")
                return None
            
            # Calculate file statistics
            file_size = file_path.stat().st_size
            file_checksum = self.integrity_manager.calculate_file_checksum(file_path)
            
            compression_ratio = None
            if compress:
                uncompressed_size = len(json_data.encode('utf-8'))
                compression_ratio = file_size / uncompressed_size
            
            # Create export summary
            export_summary = DataExportSummary(
                export_timestamp=time.time(),
                total_wallets=len(wallets),
                file_path=str(file_path),
                file_size_bytes=file_size,
                compression_ratio=compression_ratio,
                checksum=file_checksum,
                schema_version="2.0.0",
                export_format=export_format + (".gz" if compress else "")
            )
            
            # Validate export summary
            summary_dict = asdict(export_summary)
            self.schema_validator.validate_export_summary(summary_dict)
            
            # Write export summary
            summary_path = file_path.with_suffix('.summary.json')
            summary_json = json.dumps(summary_dict, indent=2, default=str)
            self.file_manager.write_file_securely(summary_path, summary_json)
            
            logger.info(f"Successfully exported {len(wallets)} wallets to {file_path}")
            logger.info(f"File size: {file_size / 1024 / 1024:.2f} MB")
            if compression_ratio:
                logger.info(f"Compression ratio: {compression_ratio:.2f}")
            
            return export_summary
            
        except Exception as e:
            logger.error(f"Export failed: {e}")
            return None
    
    def import_wallets(self, file_path: Union[str, Path]) -> Optional[List[WalletRecord]]:
        """
        Import wallet records from file.
        
        Args:
            file_path: Path to the export file
            
        Returns:
            List of wallet records or None if failed
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.error(f"Import file not found: {file_path}")
            return None
        
        try:
            # Read file
            file_data = self.file_manager.read_file_securely(file_path)
            if file_data is None:
                return None
            
            # Handle compressed files
            if file_path.suffix == '.gz':
                if isinstance(file_data, str):
                    file_data = file_data.encode('utf-8')
                file_data = gzip.decompress(file_data).decode('utf-8')
            
            # Parse JSON
            if file_path.name.endswith('.jsonl') or file_path.name.endswith('.jsonl.gz'):
                # JSON Lines format
                wallet_dicts = []
                for line in file_data.strip().split('\n'):
                    if line.strip():
                        wallet_dicts.append(json.loads(line))
            else:
                # Regular JSON format
                wallet_dicts = json.loads(file_data)
            
            # Validate and convert to WalletRecord objects
            wallets = []
            for wallet_dict in wallet_dicts:
                # Validate schema
                if not self.schema_validator.validate_wallet_record(wallet_dict):
                    logger.error("Wallet record failed validation during import")
                    return None
                
                # Verify checksum
                if not self.integrity_manager.verify_wallet_checksum(wallet_dict):
                    logger.error("Wallet record checksum verification failed")
                    return None
                
                # Convert to WalletRecord (this would need proper deserialization)
                # For now, we'll return the dictionaries
                wallets.append(wallet_dict)
            
            logger.info(f"Successfully imported {len(wallets)} wallets from {file_path}")
            return wallets
            
        except Exception as e:
            logger.error(f"Import failed: {e}")
            return None
