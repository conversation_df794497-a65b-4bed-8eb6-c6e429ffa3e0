"""
Educational Security Research Module

This module implements controlled testing environments for analyzing security
weaknesses, vulnerability detection, and educational demonstrations of poor
security practices in blockchain wallet generation.

Author: Security Research Team
License: Educational Use Only
"""

import hashlib
import secrets
import time
import math
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class VulnerabilityType(Enum):
    """Types of vulnerabilities that can be detected."""
    WEAK_ENTROPY = "weak_entropy"
    PREDICTABLE_SEQUENCE = "predictable_sequence"
    KNOWN_WEAK_KEY = "known_weak_key"
    INSUFFICIENT_RANDOMNESS = "insufficient_randomness"
    POOR_SEED_GENERATION = "poor_seed_generation"
    TIMING_ATTACK_VULNERABLE = "timing_attack_vulnerable"
    SIDE_CHANNEL_VULNERABLE = "side_channel_vulnerable"


class SecurityLevel(Enum):
    """Security levels for educational demonstrations."""
    SECURE = "secure"
    WEAK = "weak"
    VULNERABLE = "vulnerable"
    CRITICAL = "critical"


@dataclass
class VulnerabilityReport:
    """Report of detected vulnerabilities."""
    vulnerability_type: VulnerabilityType
    severity: SecurityLevel
    description: str
    affected_keys: List[str]
    detection_method: str
    remediation_advice: str
    educational_notes: str
    timestamp: float = field(default_factory=time.time)


@dataclass
class SecurityAnalysisResult:
    """Results of comprehensive security analysis."""
    total_keys_analyzed: int
    secure_keys: int
    vulnerable_keys: int
    vulnerabilities_found: List[VulnerabilityReport]
    entropy_analysis: Dict[str, Any]
    pattern_analysis: Dict[str, Any]
    recommendations: List[str]
    analysis_timestamp: float = field(default_factory=time.time)


class WeakEntropyDetector:
    """
    Detector for weak entropy patterns in key generation.
    
    Implements various statistical tests and pattern recognition algorithms
    to identify keys generated with insufficient entropy for educational purposes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize weak entropy detector."""
        self.config = config
        self.min_entropy_threshold = config.get('min_entropy_threshold', 7.0)
        self.pattern_detection_enabled = config.get('pattern_detection', True)
        
        # Load known weak patterns
        self.weak_patterns = self._load_weak_patterns()
        
        logger.info("Weak entropy detector initialized")
    
    def _load_weak_patterns(self) -> Dict[str, List[bytes]]:
        """Load known weak entropy patterns for detection."""
        patterns = {
            'sequential': [],
            'repeated': [],
            'low_complexity': [],
            'known_weak': []
        }
        
        # Sequential patterns (educational examples)
        for start in range(0, 256, 16):  # Sample every 16 values
            pattern = bytes([(start + i) % 256 for i in range(32)])
            patterns['sequential'].append(pattern)
        
        # Repeated byte patterns
        for byte_val in [0x00, 0xFF, 0xAA, 0x55, 0x01, 0x02, 0x03]:
            pattern = bytes([byte_val] * 32)
            patterns['repeated'].append(pattern)
        
        # Low complexity patterns
        for i in range(8):
            pattern = bytes([i % 4] * 32)  # Only uses 4 different byte values
            patterns['low_complexity'].append(pattern)
        
        # Known weak keys from research literature (educational examples)
        patterns['known_weak'].extend([
            bytes.fromhex('0000000000000000000000000000000000000000000000000000000000000001'),
            bytes.fromhex('0000000000000000000000000000000000000000000000000000000000000002'),
            bytes.fromhex('FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140'),
            bytes.fromhex('FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD036413F'),
        ])
        
        return patterns
    
    def analyze_key_entropy(self, private_key: bytes) -> Dict[str, Any]:
        """
        Analyze entropy quality of a private key.
        
        Args:
            private_key: 32-byte private key to analyze
            
        Returns:
            Dictionary with entropy analysis results
        """
        if len(private_key) != 32:
            raise ValueError("Private key must be exactly 32 bytes")
        
        analysis = {
            'shannon_entropy': self._calculate_shannon_entropy(private_key),
            'byte_frequency_analysis': self._analyze_byte_frequency(private_key),
            'pattern_detection': self._detect_patterns(private_key),
            'randomness_tests': self._perform_randomness_tests(private_key),
            'vulnerability_assessment': self._assess_vulnerabilities(private_key)
        }
        
        return analysis
    
    def _calculate_shannon_entropy(self, data: bytes) -> float:
        """Calculate Shannon entropy of the data."""
        if len(data) == 0:
            return 0.0
        
        # Count byte frequencies
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # Calculate entropy
        entropy = 0.0
        data_len = len(data)
        
        for count in byte_counts:
            if count > 0:
                probability = count / data_len
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _analyze_byte_frequency(self, data: bytes) -> Dict[str, Any]:
        """Analyze byte frequency distribution."""
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # Calculate statistics
        non_zero_bytes = sum(1 for count in byte_counts if count > 0)
        max_frequency = max(byte_counts)
        min_frequency = min(count for count in byte_counts if count > 0) if non_zero_bytes > 0 else 0
        
        # Chi-square test for uniformity
        expected_frequency = len(data) / 256
        chi_square = sum((count - expected_frequency) ** 2 / expected_frequency 
                        for count in byte_counts if expected_frequency > 0)
        
        return {
            'unique_bytes': non_zero_bytes,
            'max_frequency': max_frequency,
            'min_frequency': min_frequency,
            'chi_square_statistic': chi_square,
            'uniformity_score': 1.0 - (chi_square / (255 * expected_frequency)) if expected_frequency > 0 else 0.0
        }
    
    def _detect_patterns(self, data: bytes) -> Dict[str, Any]:
        """Detect various patterns in the data."""
        patterns_found = []
        
        # Check against known weak patterns
        for pattern_type, patterns in self.weak_patterns.items():
            if data in patterns:
                patterns_found.append(f'known_{pattern_type}')
        
        # Check for repeated sequences
        if self._has_repeated_sequences(data):
            patterns_found.append('repeated_sequences')
        
        # Check for arithmetic progressions
        if self._has_arithmetic_progression(data):
            patterns_found.append('arithmetic_progression')
        
        # Check for low complexity
        if len(set(data)) < len(data) * 0.2:  # Less than 20% unique bytes
            patterns_found.append('low_complexity')
        
        return {
            'patterns_detected': patterns_found,
            'pattern_count': len(patterns_found),
            'is_patterned': len(patterns_found) > 0
        }
    
    def _has_repeated_sequences(self, data: bytes, min_length: int = 4) -> bool:
        """Check for repeated byte sequences."""
        for seq_len in range(min_length, len(data) // 2 + 1):
            for start in range(len(data) - seq_len * 2 + 1):
                sequence = data[start:start + seq_len]
                # Check if this sequence repeats immediately after
                if data[start + seq_len:start + seq_len * 2] == sequence:
                    return True
        return False
    
    def _has_arithmetic_progression(self, data: bytes, min_length: int = 8) -> bool:
        """Check for arithmetic progressions in the data."""
        for start in range(len(data) - min_length + 1):
            if start + 2 >= len(data):
                break
            
            diff = (data[start + 1] - data[start]) % 256
            progression_length = 2
            
            for i in range(start + 2, len(data)):
                expected = (data[start] + diff * (i - start)) % 256
                if data[i] == expected:
                    progression_length += 1
                else:
                    break
            
            if progression_length >= min_length:
                return True
        
        return False
    
    def _perform_randomness_tests(self, data: bytes) -> Dict[str, Any]:
        """Perform statistical randomness tests."""
        # Runs test (simplified)
        runs = self._count_runs(data)
        expected_runs = (2 * len(data)) / 3
        runs_test_passed = abs(runs - expected_runs) < (expected_runs * 0.1)
        
        # Serial correlation test (simplified)
        correlation = self._calculate_serial_correlation(data)
        
        return {
            'runs_count': runs,
            'expected_runs': expected_runs,
            'runs_test_passed': runs_test_passed,
            'serial_correlation': correlation,
            'correlation_acceptable': abs(correlation) < 0.1
        }
    
    def _count_runs(self, data: bytes) -> int:
        """Count runs of consecutive identical bytes."""
        if len(data) <= 1:
            return len(data)
        
        runs = 1
        for i in range(1, len(data)):
            if data[i] != data[i - 1]:
                runs += 1
        
        return runs
    
    def _calculate_serial_correlation(self, data: bytes) -> float:
        """Calculate serial correlation coefficient."""
        if len(data) < 2:
            return 0.0
        
        n = len(data)
        sum_x = sum(data)
        sum_x2 = sum(x * x for x in data)
        sum_xy = sum(data[i] * data[(i + 1) % n] for i in range(n))
        
        mean_x = sum_x / n
        variance_x = (sum_x2 / n) - (mean_x * mean_x)
        
        if variance_x == 0:
            return 0.0
        
        covariance = (sum_xy / n) - (mean_x * mean_x)
        correlation = covariance / variance_x
        
        return correlation
    
    def _assess_vulnerabilities(self, data: bytes) -> List[VulnerabilityType]:
        """Assess potential vulnerabilities in the key."""
        vulnerabilities = []
        
        # Check entropy
        entropy = self._calculate_shannon_entropy(data)
        if entropy < self.min_entropy_threshold:
            vulnerabilities.append(VulnerabilityType.WEAK_ENTROPY)
        
        # Check for patterns
        pattern_analysis = self._detect_patterns(data)
        if pattern_analysis['is_patterned']:
            if 'arithmetic_progression' in pattern_analysis['patterns_detected']:
                vulnerabilities.append(VulnerabilityType.PREDICTABLE_SEQUENCE)
            if any('known_' in pattern for pattern in pattern_analysis['patterns_detected']):
                vulnerabilities.append(VulnerabilityType.KNOWN_WEAK_KEY)
        
        # Check randomness
        randomness_tests = self._perform_randomness_tests(data)
        if not randomness_tests['runs_test_passed'] or not randomness_tests['correlation_acceptable']:
            vulnerabilities.append(VulnerabilityType.INSUFFICIENT_RANDOMNESS)
        
        return vulnerabilities


class VulnerabilityAnalyzer:
    """
    Comprehensive vulnerability analyzer for educational security research.
    
    Analyzes generated keys for various security weaknesses and provides
    detailed reports for educational purposes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize vulnerability analyzer."""
        self.config = config
        self.entropy_detector = WeakEntropyDetector(config)
        self.known_vulnerable_ranges = self._load_vulnerable_ranges()
        self.analysis_history: List[SecurityAnalysisResult] = []
        
        logger.info("Vulnerability analyzer initialized")
    
    def _load_vulnerable_ranges(self) -> List[Tuple[int, int]]:
        """Load known vulnerable key ranges from security research."""
        # These are well-documented vulnerable ranges for educational purposes
        vulnerable_ranges = [
            (1, 100),  # Very small keys
            (0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140 - 100,
             0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140),  # Near curve order
        ]
        
        return vulnerable_ranges
    
    def analyze_key_security(self, private_key: bytes) -> List[VulnerabilityReport]:
        """
        Perform comprehensive security analysis of a private key.
        
        Args:
            private_key: 32-byte private key to analyze
            
        Returns:
            List of vulnerability reports
        """
        vulnerabilities = []
        
        # Convert to integer for range checks
        key_int = int.from_bytes(private_key, 'big')
        
        # Check against known vulnerable ranges
        for start, end in self.known_vulnerable_ranges:
            if start <= key_int <= end:
                vulnerabilities.append(VulnerabilityReport(
                    vulnerability_type=VulnerabilityType.KNOWN_WEAK_KEY,
                    severity=SecurityLevel.CRITICAL,
                    description=f"Private key falls within known vulnerable range [{start}, {end}]",
                    affected_keys=[private_key.hex()],
                    detection_method="Range analysis",
                    remediation_advice="Generate a new private key using cryptographically secure random number generator",
                    educational_notes="Keys in this range are known to be vulnerable due to their predictable nature"
                ))
        
        # Perform entropy analysis
        entropy_analysis = self.entropy_detector.analyze_key_entropy(private_key)
        
        # Check for entropy-related vulnerabilities
        for vuln_type in entropy_analysis['vulnerability_assessment']:
            severity = self._determine_severity(vuln_type, entropy_analysis)
            
            vulnerabilities.append(VulnerabilityReport(
                vulnerability_type=vuln_type,
                severity=severity,
                description=self._get_vulnerability_description(vuln_type, entropy_analysis),
                affected_keys=[private_key.hex()],
                detection_method="Entropy analysis",
                remediation_advice=self._get_remediation_advice(vuln_type),
                educational_notes=self._get_educational_notes(vuln_type)
            ))
        
        return vulnerabilities
    
    def _determine_severity(self, vuln_type: VulnerabilityType, analysis: Dict[str, Any]) -> SecurityLevel:
        """Determine severity level of a vulnerability."""
        if vuln_type == VulnerabilityType.KNOWN_WEAK_KEY:
            return SecurityLevel.CRITICAL
        elif vuln_type == VulnerabilityType.WEAK_ENTROPY:
            entropy = analysis['shannon_entropy']
            if entropy < 4.0:
                return SecurityLevel.CRITICAL
            elif entropy < 6.0:
                return SecurityLevel.VULNERABLE
            else:
                return SecurityLevel.WEAK
        elif vuln_type == VulnerabilityType.PREDICTABLE_SEQUENCE:
            return SecurityLevel.VULNERABLE
        else:
            return SecurityLevel.WEAK
    
    def _get_vulnerability_description(self, vuln_type: VulnerabilityType, analysis: Dict[str, Any]) -> str:
        """Get description for a vulnerability type."""
        descriptions = {
            VulnerabilityType.WEAK_ENTROPY: f"Low entropy detected (Shannon entropy: {analysis['shannon_entropy']:.2f})",
            VulnerabilityType.PREDICTABLE_SEQUENCE: "Predictable sequence pattern detected in private key",
            VulnerabilityType.KNOWN_WEAK_KEY: "Private key matches known weak key pattern",
            VulnerabilityType.INSUFFICIENT_RANDOMNESS: "Statistical randomness tests failed",
            VulnerabilityType.POOR_SEED_GENERATION: "Poor quality random seed detected"
        }
        
        return descriptions.get(vuln_type, f"Vulnerability of type {vuln_type.value} detected")
    
    def _get_remediation_advice(self, vuln_type: VulnerabilityType) -> str:
        """Get remediation advice for a vulnerability type."""
        advice = {
            VulnerabilityType.WEAK_ENTROPY: "Use a cryptographically secure random number generator with proper entropy sources",
            VulnerabilityType.PREDICTABLE_SEQUENCE: "Avoid using predictable sequences or patterns in key generation",
            VulnerabilityType.KNOWN_WEAK_KEY: "Never use known weak keys; always generate new random keys",
            VulnerabilityType.INSUFFICIENT_RANDOMNESS: "Improve randomness quality by using better entropy sources",
            VulnerabilityType.POOR_SEED_GENERATION: "Use high-quality entropy sources for seeding random number generators"
        }
        
        return advice.get(vuln_type, "Consult security best practices for key generation")
    
    def _get_educational_notes(self, vuln_type: VulnerabilityType) -> str:
        """Get educational notes for a vulnerability type."""
        notes = {
            VulnerabilityType.WEAK_ENTROPY: "Entropy is a measure of randomness. Low entropy makes keys predictable and vulnerable to attacks.",
            VulnerabilityType.PREDICTABLE_SEQUENCE: "Predictable patterns in cryptographic keys can be exploited by attackers to guess or derive the key.",
            VulnerabilityType.KNOWN_WEAK_KEY: "Some private keys are known to be weak due to their mathematical properties or because they've been compromised.",
            VulnerabilityType.INSUFFICIENT_RANDOMNESS: "Cryptographic security relies on unpredictability. Poor randomness undermines this foundation.",
            VulnerabilityType.POOR_SEED_GENERATION: "The quality of random number generation depends heavily on the initial seed value."
        }
        
        return notes.get(vuln_type, "This vulnerability type represents a potential security weakness in key generation.")
    
    def perform_batch_analysis(self, private_keys: List[bytes]) -> SecurityAnalysisResult:
        """
        Perform security analysis on a batch of private keys.
        
        Args:
            private_keys: List of private keys to analyze
            
        Returns:
            Comprehensive security analysis result
        """
        all_vulnerabilities = []
        secure_count = 0
        vulnerable_count = 0
        
        # Analyze each key
        for private_key in private_keys:
            key_vulnerabilities = self.analyze_key_security(private_key)
            
            if key_vulnerabilities:
                vulnerable_count += 1
                all_vulnerabilities.extend(key_vulnerabilities)
            else:
                secure_count += 1
        
        # Aggregate entropy analysis
        entropy_values = []
        pattern_counts = []
        
        for private_key in private_keys:
            entropy_analysis = self.entropy_detector.analyze_key_entropy(private_key)
            entropy_values.append(entropy_analysis['shannon_entropy'])
            pattern_counts.append(entropy_analysis['pattern_detection']['pattern_count'])
        
        # Generate recommendations
        recommendations = self._generate_recommendations(all_vulnerabilities, entropy_values)
        
        # Create analysis result
        result = SecurityAnalysisResult(
            total_keys_analyzed=len(private_keys),
            secure_keys=secure_count,
            vulnerable_keys=vulnerable_count,
            vulnerabilities_found=all_vulnerabilities,
            entropy_analysis={
                'average_entropy': sum(entropy_values) / len(entropy_values) if entropy_values else 0.0,
                'min_entropy': min(entropy_values) if entropy_values else 0.0,
                'max_entropy': max(entropy_values) if entropy_values else 0.0,
                'entropy_distribution': self._calculate_entropy_distribution(entropy_values)
            },
            pattern_analysis={
                'keys_with_patterns': sum(1 for count in pattern_counts if count > 0),
                'average_patterns_per_key': sum(pattern_counts) / len(pattern_counts) if pattern_counts else 0.0,
                'pattern_distribution': self._calculate_pattern_distribution(pattern_counts)
            },
            recommendations=recommendations
        )
        
        self.analysis_history.append(result)
        
        logger.info(f"Batch analysis completed: {len(private_keys)} keys analyzed, "
                   f"{vulnerable_count} vulnerabilities found")
        
        return result
    
    def _calculate_entropy_distribution(self, entropy_values: List[float]) -> Dict[str, int]:
        """Calculate distribution of entropy values."""
        distribution = {
            'very_low': 0,    # < 4.0
            'low': 0,         # 4.0 - 6.0
            'medium': 0,      # 6.0 - 7.0
            'high': 0,        # 7.0 - 7.5
            'very_high': 0    # > 7.5
        }
        
        for entropy in entropy_values:
            if entropy < 4.0:
                distribution['very_low'] += 1
            elif entropy < 6.0:
                distribution['low'] += 1
            elif entropy < 7.0:
                distribution['medium'] += 1
            elif entropy < 7.5:
                distribution['high'] += 1
            else:
                distribution['very_high'] += 1
        
        return distribution
    
    def _calculate_pattern_distribution(self, pattern_counts: List[int]) -> Dict[str, int]:
        """Calculate distribution of pattern counts."""
        distribution = {
            'no_patterns': 0,
            'few_patterns': 0,    # 1-2 patterns
            'many_patterns': 0    # 3+ patterns
        }
        
        for count in pattern_counts:
            if count == 0:
                distribution['no_patterns'] += 1
            elif count <= 2:
                distribution['few_patterns'] += 1
            else:
                distribution['many_patterns'] += 1
        
        return distribution
    
    def _generate_recommendations(self, vulnerabilities: List[VulnerabilityReport], 
                                entropy_values: List[float]) -> List[str]:
        """Generate security recommendations based on analysis results."""
        recommendations = []
        
        # Check for common vulnerability types
        vuln_types = [v.vulnerability_type for v in vulnerabilities]
        
        if VulnerabilityType.WEAK_ENTROPY in vuln_types:
            recommendations.append("Improve entropy quality by using cryptographically secure random number generators")
        
        if VulnerabilityType.PREDICTABLE_SEQUENCE in vuln_types:
            recommendations.append("Avoid using predictable patterns or sequences in key generation")
        
        if VulnerabilityType.KNOWN_WEAK_KEY in vuln_types:
            recommendations.append("Implement checks against known weak key databases")
        
        # Check entropy statistics
        if entropy_values:
            avg_entropy = sum(entropy_values) / len(entropy_values)
            if avg_entropy < 7.0:
                recommendations.append("Overall entropy quality is below recommended threshold (7.0 bits)")
        
        # General recommendations
        if vulnerabilities:
            recommendations.append("Consider implementing additional security measures for key generation")
            recommendations.append("Regular security audits of key generation processes are recommended")
        
        if not recommendations:
            recommendations.append("Key generation appears to follow security best practices")
        
        return recommendations
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get summary of all analyses performed."""
        if not self.analysis_history:
            return {"message": "No analyses performed yet"}
        
        total_keys = sum(result.total_keys_analyzed for result in self.analysis_history)
        total_vulnerabilities = sum(len(result.vulnerabilities_found) for result in self.analysis_history)
        
        return {
            "total_analyses": len(self.analysis_history),
            "total_keys_analyzed": total_keys,
            "total_vulnerabilities_found": total_vulnerabilities,
            "vulnerability_rate": total_vulnerabilities / max(total_keys, 1),
            "most_recent_analysis": self.analysis_history[-1].analysis_timestamp,
            "common_vulnerability_types": self._get_common_vulnerability_types()
        }
    
    def _get_common_vulnerability_types(self) -> Dict[str, int]:
        """Get count of common vulnerability types across all analyses."""
        vuln_counts = {}
        
        for result in self.analysis_history:
            for vuln in result.vulnerabilities_found:
                vuln_type = vuln.vulnerability_type.value
                vuln_counts[vuln_type] = vuln_counts.get(vuln_type, 0) + 1
        
        return vuln_counts
