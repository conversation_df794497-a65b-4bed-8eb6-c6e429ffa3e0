"""
Performance and Scalability Optimization Module

This module implements efficient batch processing, multi-threading with thread safety,
progress tracking, and performance monitoring for large-scale wallet generation.

Author: Security Research Team
License: Educational Use Only
"""

import asyncio
import threading
import time
import gc
import psutil
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Optional, Any, Callable, Iterator, Tuple
from dataclasses import dataclass, field
from queue import Queue, Empty
from threading import Lock, Event
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring system performance."""
    start_time: float
    end_time: Optional[float] = None
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    operations_per_second: float = 0.0
    average_operation_time_ms: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    cache_hit_rate: float = 0.0
    api_calls_made: int = 0
    errors_encountered: List[str] = field(default_factory=list)


@dataclass
class BatchResult:
    """Result of a batch processing operation."""
    batch_id: int
    start_time: float
    end_time: float
    items_processed: int
    successful_items: int
    failed_items: int
    results: List[Any]
    errors: List[str]
    metrics: PerformanceMetrics


@dataclass
class ProgressInfo:
    """Progress tracking information."""
    current_item: int
    total_items: int
    percentage: float
    estimated_completion_time: Optional[datetime]
    elapsed_time: timedelta
    items_per_second: float
    batches_completed: int
    total_batches: int


class ProgressTracker:
    """
    Advanced progress tracking with ETA calculation and performance monitoring.
    
    Provides real-time progress updates, estimated completion time,
    and performance statistics for long-running operations.
    """
    
    def __init__(self, total_items: int, update_interval: float = 1.0):
        """
        Initialize progress tracker.
        
        Args:
            total_items: Total number of items to process
            update_interval: Minimum interval between progress updates (seconds)
        """
        self.total_items = total_items
        self.update_interval = update_interval
        self.start_time = time.time()
        self.last_update_time = 0.0
        
        self.current_item = 0
        self.successful_items = 0
        self.failed_items = 0
        self.batches_completed = 0
        self.total_batches = 0
        
        self.operation_times = []
        self.lock = Lock()
        
        # Callbacks for progress updates
        self.progress_callbacks: List[Callable[[ProgressInfo], None]] = []
        
        logger.info(f"Progress tracker initialized for {total_items} items")
    
    def add_progress_callback(self, callback: Callable[[ProgressInfo], None]) -> None:
        """Add a callback function to be called on progress updates."""
        self.progress_callbacks.append(callback)
    
    def update(self, items_processed: int = 1, success: bool = True, operation_time: Optional[float] = None) -> None:
        """
        Update progress with processed items.
        
        Args:
            items_processed: Number of items processed in this update
            success: Whether the operation was successful
            operation_time: Time taken for the operation (seconds)
        """
        with self.lock:
            self.current_item += items_processed
            
            if success:
                self.successful_items += items_processed
            else:
                self.failed_items += items_processed
            
            if operation_time is not None:
                self.operation_times.append(operation_time)
                # Keep only last 1000 operation times for memory efficiency
                if len(self.operation_times) > 1000:
                    self.operation_times = self.operation_times[-1000:]
            
            # Check if we should send progress update
            current_time = time.time()
            if current_time - self.last_update_time >= self.update_interval:
                self._send_progress_update()
                self.last_update_time = current_time
    
    def update_batch_completion(self) -> None:
        """Update batch completion count."""
        with self.lock:
            self.batches_completed += 1
            self._send_progress_update()
    
    def set_total_batches(self, total_batches: int) -> None:
        """Set total number of batches."""
        self.total_batches = total_batches
    
    def _send_progress_update(self) -> None:
        """Send progress update to all registered callbacks."""
        progress_info = self.get_progress_info()
        
        for callback in self.progress_callbacks:
            try:
                callback(progress_info)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    def get_progress_info(self) -> ProgressInfo:
        """Get current progress information."""
        with self.lock:
            current_time = time.time()
            elapsed_time = timedelta(seconds=current_time - self.start_time)
            
            # Calculate percentage
            percentage = (self.current_item / self.total_items) * 100 if self.total_items > 0 else 0
            
            # Calculate items per second
            items_per_second = self.current_item / max(elapsed_time.total_seconds(), 0.001)
            
            # Estimate completion time
            estimated_completion_time = None
            if items_per_second > 0 and self.current_item < self.total_items:
                remaining_items = self.total_items - self.current_item
                remaining_seconds = remaining_items / items_per_second
                estimated_completion_time = datetime.now() + timedelta(seconds=remaining_seconds)
            
            return ProgressInfo(
                current_item=self.current_item,
                total_items=self.total_items,
                percentage=percentage,
                estimated_completion_time=estimated_completion_time,
                elapsed_time=elapsed_time,
                items_per_second=items_per_second,
                batches_completed=self.batches_completed,
                total_batches=self.total_batches
            )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics."""
        with self.lock:
            elapsed_time = time.time() - self.start_time
            
            avg_operation_time = 0.0
            if self.operation_times:
                avg_operation_time = sum(self.operation_times) / len(self.operation_times)
            
            return {
                "total_items": self.total_items,
                "processed_items": self.current_item,
                "successful_items": self.successful_items,
                "failed_items": self.failed_items,
                "success_rate": self.successful_items / max(self.current_item, 1),
                "elapsed_time_seconds": elapsed_time,
                "items_per_second": self.current_item / max(elapsed_time, 0.001),
                "average_operation_time_ms": avg_operation_time * 1000,
                "batches_completed": self.batches_completed,
                "total_batches": self.total_batches
            }


class BatchProcessor:
    """
    Efficient batch processing system with configurable batch sizes and concurrency.
    
    Implements intelligent batching with dynamic batch size adjustment based on
    performance metrics and system resources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize batch processor.
        
        Args:
            config: Configuration dictionary with batch processing settings
        """
        self.config = config
        self.batch_size = config.get('batch_size', 100)
        self.max_concurrent_batches = config.get('max_concurrent_batches', 5)
        self.adaptive_batch_size = config.get('adaptive_batch_size', True)
        self.min_batch_size = config.get('min_batch_size', 10)
        self.max_batch_size = config.get('max_batch_size', 1000)
        
        self.batch_results: List[BatchResult] = []
        self.current_batch_id = 0
        self.lock = Lock()
        
        logger.info(f"BatchProcessor initialized with batch_size={self.batch_size}, "
                   f"max_concurrent_batches={self.max_concurrent_batches}")
    
    def create_batches(self, items: List[Any]) -> List[List[Any]]:
        """
        Create batches from a list of items.
        
        Args:
            items: List of items to batch
            
        Returns:
            List of batches
        """
        batches = []
        current_batch_size = self.batch_size
        
        for i in range(0, len(items), current_batch_size):
            batch = items[i:i + current_batch_size]
            batches.append(batch)
            
            # Adaptive batch sizing based on performance
            if self.adaptive_batch_size and len(self.batch_results) > 0:
                current_batch_size = self._calculate_optimal_batch_size()
        
        logger.info(f"Created {len(batches)} batches from {len(items)} items")
        return batches
    
    def _calculate_optimal_batch_size(self) -> int:
        """Calculate optimal batch size based on recent performance."""
        if len(self.batch_results) < 3:
            return self.batch_size
        
        # Analyze recent batch performance
        recent_results = self.batch_results[-5:]  # Last 5 batches
        
        # Calculate average processing time per item
        total_time = sum(r.end_time - r.start_time for r in recent_results)
        total_items = sum(r.items_processed for r in recent_results)
        
        if total_items == 0:
            return self.batch_size
        
        avg_time_per_item = total_time / total_items
        
        # Adjust batch size based on performance
        # Target: 1-5 seconds per batch
        target_batch_time = 3.0  # seconds
        optimal_batch_size = int(target_batch_time / max(avg_time_per_item, 0.001))
        
        # Clamp to configured limits
        optimal_batch_size = max(self.min_batch_size, 
                               min(self.max_batch_size, optimal_batch_size))
        
        logger.debug(f"Adjusted batch size to {optimal_batch_size} "
                    f"(avg_time_per_item={avg_time_per_item:.3f}s)")
        
        return optimal_batch_size
    
    async def process_batches_async(self, 
                                  batches: List[List[Any]], 
                                  processor_func: Callable[[List[Any], int], Any],
                                  progress_tracker: Optional[ProgressTracker] = None) -> List[BatchResult]:
        """
        Process batches asynchronously with controlled concurrency.
        
        Args:
            batches: List of batches to process
            processor_func: Function to process each batch
            progress_tracker: Optional progress tracker
            
        Returns:
            List of batch results
        """
        if progress_tracker:
            progress_tracker.set_total_batches(len(batches))
        
        semaphore = asyncio.Semaphore(self.max_concurrent_batches)
        tasks = []
        
        for i, batch in enumerate(batches):
            task = self._process_single_batch_async(
                batch, i, processor_func, semaphore, progress_tracker
            )
            tasks.append(task)
        
        # Wait for all batches to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and collect valid results
        valid_results = []
        for result in results:
            if isinstance(result, BatchResult):
                valid_results.append(result)
            else:
                logger.error(f"Batch processing error: {result}")
        
        return valid_results
    
    async def _process_single_batch_async(self,
                                        batch: List[Any],
                                        batch_id: int,
                                        processor_func: Callable[[List[Any], int], Any],
                                        semaphore: asyncio.Semaphore,
                                        progress_tracker: Optional[ProgressTracker]) -> BatchResult:
        """Process a single batch asynchronously."""
        async with semaphore:
            start_time = time.time()
            
            try:
                # Run processor function in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, processor_func, batch, batch_id)
                
                end_time = time.time()
                
                # Create batch result
                batch_result = BatchResult(
                    batch_id=batch_id,
                    start_time=start_time,
                    end_time=end_time,
                    items_processed=len(batch),
                    successful_items=len(batch),  # Assume success if no exception
                    failed_items=0,
                    results=[result] if result is not None else [],
                    errors=[],
                    metrics=PerformanceMetrics(
                        start_time=start_time,
                        end_time=end_time,
                        total_operations=len(batch),
                        successful_operations=len(batch),
                        failed_operations=0
                    )
                )
                
                # Update progress
                if progress_tracker:
                    progress_tracker.update(len(batch), True, end_time - start_time)
                    progress_tracker.update_batch_completion()
                
                with self.lock:
                    self.batch_results.append(batch_result)
                
                return batch_result
                
            except Exception as e:
                end_time = time.time()
                error_msg = str(e)
                
                logger.error(f"Batch {batch_id} failed: {error_msg}")
                
                batch_result = BatchResult(
                    batch_id=batch_id,
                    start_time=start_time,
                    end_time=end_time,
                    items_processed=len(batch),
                    successful_items=0,
                    failed_items=len(batch),
                    results=[],
                    errors=[error_msg],
                    metrics=PerformanceMetrics(
                        start_time=start_time,
                        end_time=end_time,
                        total_operations=len(batch),
                        successful_operations=0,
                        failed_operations=len(batch),
                        errors_encountered=[error_msg]
                    )
                )
                
                # Update progress
                if progress_tracker:
                    progress_tracker.update(len(batch), False, end_time - start_time)
                    progress_tracker.update_batch_completion()
                
                with self.lock:
                    self.batch_results.append(batch_result)
                
                return batch_result


class MemoryManager:
    """
    Memory management system for handling large-scale operations.
    
    Monitors memory usage, performs garbage collection, and implements
    memory-efficient data structures for processing large datasets.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize memory manager."""
        self.config = config
        self.max_memory_mb = config.get('max_memory_mb', 1024)
        self.gc_threshold_mb = config.get('gc_threshold_mb', 512)
        self.gc_interval = config.get('gc_interval', 1000)
        self.clear_sensitive_data = config.get('clear_sensitive_data', True)
        
        self.operation_count = 0
        self.last_gc_time = time.time()
        self.memory_samples = []
        
        logger.info(f"MemoryManager initialized with max_memory={self.max_memory_mb}MB")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    
    def check_memory_pressure(self) -> bool:
        """Check if system is under memory pressure."""
        memory_usage = self.get_memory_usage()
        
        # Check if we're approaching memory limits
        if memory_usage['rss_mb'] > self.max_memory_mb:
            logger.warning(f"Memory usage ({memory_usage['rss_mb']:.1f}MB) "
                          f"exceeds limit ({self.max_memory_mb}MB)")
            return True
        
        if memory_usage['percent'] > 80:
            logger.warning(f"Memory usage at {memory_usage['percent']:.1f}%")
            return True
        
        return False
    
    def perform_garbage_collection(self, force: bool = False) -> Dict[str, Any]:
        """Perform garbage collection if needed."""
        current_time = time.time()
        memory_before = self.get_memory_usage()
        
        should_gc = (
            force or
            self.operation_count >= self.gc_interval or
            current_time - self.last_gc_time > 300 or  # 5 minutes
            memory_before['rss_mb'] > self.gc_threshold_mb
        )
        
        if should_gc:
            logger.debug("Performing garbage collection")
            
            # Clear sensitive data if configured
            if self.clear_sensitive_data:
                self._clear_sensitive_data()
            
            # Force garbage collection
            collected = gc.collect()
            
            memory_after = self.get_memory_usage()
            memory_freed = memory_before['rss_mb'] - memory_after['rss_mb']
            
            self.operation_count = 0
            self.last_gc_time = current_time
            
            gc_result = {
                'objects_collected': collected,
                'memory_before_mb': memory_before['rss_mb'],
                'memory_after_mb': memory_after['rss_mb'],
                'memory_freed_mb': memory_freed,
                'timestamp': current_time
            }
            
            logger.info(f"GC completed: freed {memory_freed:.1f}MB, "
                       f"collected {collected} objects")
            
            return gc_result
        
        return {}
    
    def _clear_sensitive_data(self) -> None:
        """Clear sensitive data from memory (placeholder implementation)."""
        # In a real implementation, this would clear specific sensitive data structures
        # For now, we'll just ensure all unreferenced objects are collected
        pass
    
    def track_operation(self) -> None:
        """Track an operation for memory management purposes."""
        self.operation_count += 1
        
        # Sample memory usage periodically
        if self.operation_count % 100 == 0:
            memory_usage = self.get_memory_usage()
            self.memory_samples.append({
                'timestamp': time.time(),
                'rss_mb': memory_usage['rss_mb'],
                'operation_count': self.operation_count
            })
            
            # Keep only last 1000 samples
            if len(self.memory_samples) > 1000:
                self.memory_samples = self.memory_samples[-1000:]
            
            # Check for memory pressure
            if self.check_memory_pressure():
                self.perform_garbage_collection(force=True)
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        current_usage = self.get_memory_usage()
        
        if self.memory_samples:
            peak_usage = max(sample['rss_mb'] for sample in self.memory_samples)
            avg_usage = sum(sample['rss_mb'] for sample in self.memory_samples) / len(self.memory_samples)
        else:
            peak_usage = current_usage['rss_mb']
            avg_usage = current_usage['rss_mb']
        
        return {
            'current_usage_mb': current_usage['rss_mb'],
            'peak_usage_mb': peak_usage,
            'average_usage_mb': avg_usage,
            'memory_limit_mb': self.max_memory_mb,
            'utilization_percent': (current_usage['rss_mb'] / self.max_memory_mb) * 100,
            'operations_tracked': self.operation_count,
            'gc_threshold_mb': self.gc_threshold_mb,
            'samples_collected': len(self.memory_samples)
        }


class PerformanceMonitor:
    """
    Comprehensive performance monitoring system.
    
    Tracks system performance, resource usage, and operation metrics
    to provide insights for optimization and troubleshooting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize performance monitor."""
        self.config = config
        self.monitoring_enabled = config.get('performance_tracking', True)
        self.sample_interval = config.get('sample_interval', 5.0)
        
        self.start_time = time.time()
        self.samples = []
        self.operation_metrics = []
        
        # Start monitoring thread if enabled
        if self.monitoring_enabled:
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.stop_event = Event()
            self.monitoring_thread.start()
            
            logger.info("Performance monitoring started")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop running in background thread."""
        while not self.stop_event.is_set():
            try:
                sample = self._collect_performance_sample()
                self.samples.append(sample)
                
                # Keep only last 1000 samples
                if len(self.samples) > 1000:
                    self.samples = self.samples[-1000:]
                
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
            
            self.stop_event.wait(self.sample_interval)
    
    def _collect_performance_sample(self) -> Dict[str, Any]:
        """Collect a performance sample."""
        current_time = time.time()
        process = psutil.Process(os.getpid())
        
        # CPU usage
        cpu_percent = process.cpu_percent()
        
        # Memory usage
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        # System-wide metrics
        system_cpu = psutil.cpu_percent()
        system_memory = psutil.virtual_memory()
        
        return {
            'timestamp': current_time,
            'process_cpu_percent': cpu_percent,
            'process_memory_mb': memory_mb,
            'system_cpu_percent': system_cpu,
            'system_memory_percent': system_memory.percent,
            'system_memory_available_mb': system_memory.available / 1024 / 1024
        }
    
    def record_operation_metrics(self, metrics: PerformanceMetrics) -> None:
        """Record metrics for a completed operation."""
        self.operation_metrics.append(metrics)
        
        # Keep only last 10000 operation metrics
        if len(self.operation_metrics) > 10000:
            self.operation_metrics = self.operation_metrics[-10000:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        current_time = time.time()
        uptime = current_time - self.start_time
        
        summary = {
            'uptime_seconds': uptime,
            'monitoring_enabled': self.monitoring_enabled,
            'samples_collected': len(self.samples),
            'operations_recorded': len(self.operation_metrics)
        }
        
        # System performance summary
        if self.samples:
            recent_samples = [s for s in self.samples if current_time - s['timestamp'] <= 300]  # Last 5 minutes
            
            if recent_samples:
                summary.update({
                    'avg_cpu_percent': sum(s['process_cpu_percent'] for s in recent_samples) / len(recent_samples),
                    'avg_memory_mb': sum(s['process_memory_mb'] for s in recent_samples) / len(recent_samples),
                    'peak_memory_mb': max(s['process_memory_mb'] for s in recent_samples),
                    'avg_system_cpu_percent': sum(s['system_cpu_percent'] for s in recent_samples) / len(recent_samples)
                })
        
        # Operation performance summary
        if self.operation_metrics:
            recent_ops = [m for m in self.operation_metrics if current_time - m.start_time <= 3600]  # Last hour
            
            if recent_ops:
                total_ops = sum(m.total_operations for m in recent_ops)
                successful_ops = sum(m.successful_operations for m in recent_ops)
                
                summary.update({
                    'total_operations_last_hour': total_ops,
                    'successful_operations_last_hour': successful_ops,
                    'success_rate_last_hour': successful_ops / max(total_ops, 1),
                    'avg_operations_per_second': sum(m.operations_per_second for m in recent_ops) / len(recent_ops)
                })
        
        return summary
    
    def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        if hasattr(self, 'stop_event'):
            self.stop_event.set()
            if hasattr(self, 'monitoring_thread'):
                self.monitoring_thread.join(timeout=5.0)
            logger.info("Performance monitoring stopped")
